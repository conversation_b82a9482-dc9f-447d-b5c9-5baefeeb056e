// 明主题样式定义

// 默认主题为明主题
:root,
[data-theme="light"] {
  // 基础颜色
  --bg-primary: #{$light-background};
  --bg-secondary: #{$light-surface};
  --text-primary: #{$light-text};
  --text-secondary: #{$light-text-light};
  --border-color: #{$light-border};
  
  // 组件颜色
  --navbar-bg: #{rgba($white, 0.95)};
  --card-bg: #{$white};
  --footer-bg: #{$grey-darker};
  --footer-text: #{$white-ter};
  
  // 阴影
  --shadow-light: #{$shadow-light};
  --shadow-medium: #{$shadow};
  --shadow-large: #{$shadow-large};
}

// 明主题特定样式
[data-theme="light"] {
  body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
  }
  
  .navbar {
    background-color: var(--navbar-bg);
    
    .navbar-item {
      color: var(--text-primary);
      
      &:hover {
        color: $primary;
        background-color: transparent;
      }
    }
    
    .navbar-burger {
      color: var(--text-primary);
    }
  }
  
  .card {
    background-color: var(--card-bg);
    color: var(--text-primary);
    box-shadow: var(--shadow-light);
    
    &:hover {
      box-shadow: var(--shadow-medium);
    }
  }
  
  .section {
    background-color: var(--bg-primary);
    
    &.is-alternate {
      background-color: var(--bg-secondary);
    }
  }
  
  .footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
  }
  
  .filter-bar {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    
    .filter-option {
      background-color: var(--card-bg);
      border-color: var(--border-color);
      color: var(--text-primary);
      
      &:hover {
        border-color: $primary;
        color: $primary;
      }
      
      &.is-active {
        background-color: $primary;
        border-color: $primary;
        color: $white;
      }
    }
  }
  
  .timeline {
    &::before {
      background-color: var(--border-color);
    }
    
    .timeline-item {
      .timeline-content {
        background-color: var(--card-bg);
        color: var(--text-primary);
        box-shadow: var(--shadow-light);
      }
    }
  }
  
  .breadcrumb {
    a {
      color: var(--text-secondary);
      
      &:hover {
        color: $primary;
      }
    }
    
    .is-active a {
      color: var(--text-primary);
    }
  }
  
  .input,
  .textarea,
  .select select {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-primary);
    
    &:focus {
      border-color: $primary;
      box-shadow: 0 0 0 0.125em rgba($primary, 0.25);
    }
    
    &::placeholder {
      color: var(--text-secondary);
    }
  }
  
  .table {
    background-color: var(--card-bg);
    color: var(--text-primary);
    
    th {
      background-color: var(--bg-secondary);
      color: var(--text-primary);
      border-bottom-color: var(--border-color);
    }
    
    td {
      border-bottom-color: var(--border-color);
    }
    
    tbody tr:hover {
      background-color: var(--bg-secondary);
    }
  }
  
  .modal-card {
    background-color: var(--card-bg);
    
    .modal-card-head,
    .modal-card-foot {
      background-color: var(--bg-secondary);
      border-color: var(--border-color);
    }
    
    .modal-card-title {
      color: var(--text-primary);
    }
    
    .modal-card-body {
      background-color: var(--card-bg);
      color: var(--text-primary);
    }
  }
  
  .dropdown-content {
    background-color: var(--card-bg);
    box-shadow: var(--shadow-medium);
    
    .dropdown-item {
      color: var(--text-primary);
      
      &:hover {
        background-color: var(--bg-secondary);
      }
    }
  }
  
  .notification {
    &.is-light {
      background-color: var(--bg-secondary);
      color: var(--text-primary);
    }
  }
  
  .message {
    background-color: var(--bg-secondary);
    
    .message-header {
      background-color: $primary;
      color: $white;
    }
    
    .message-body {
      background-color: var(--card-bg);
      color: var(--text-primary);
      border-color: var(--border-color);
    }
  }
  
  .tabs {
    a {
      color: var(--text-secondary);
      border-bottom-color: var(--border-color);
      
      &:hover {
        color: var(--text-primary);
        border-bottom-color: var(--text-secondary);
      }
    }
    
    .is-active a {
      color: $primary;
      border-bottom-color: $primary;
    }
  }
  
  .progress {
    background-color: var(--border-color);
    
    &::-webkit-progress-value {
      background-color: $primary;
    }
    
    &::-moz-progress-bar {
      background-color: $primary;
    }
  }
  
  // 代码块样式
  pre {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
  }
  
  code {
    background-color: var(--bg-secondary);
    color: $primary;
  }
  
  // 引用样式
  blockquote {
    background-color: var(--bg-secondary);
    border-left: 4px solid $primary;
    color: var(--text-primary);
  }
  
  // 分割线
  hr {
    background-color: var(--border-color);
  }
  
  // 滚动条样式
  ::-webkit-scrollbar-track {
    background-color: var(--bg-secondary);
  }
  
  ::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    
    &:hover {
      background-color: var(--text-secondary);
    }
  }
}

// 主题切换功能

class ThemeManager {
    constructor() {
        this.currentTheme = 'light';
        this.themeKey = 'building-tech-theme';
        this.init();
    }
    
    init() {
        // 从localStorage获取保存的主题
        const savedTheme = localStorage.getItem(this.themeKey);
        
        // 检查系统主题偏好
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        // 确定初始主题
        if (savedTheme) {
            this.currentTheme = savedTheme;
        } else if (systemPrefersDark) {
            this.currentTheme = 'dark';
        }
        
        // 应用主题
        this.applyTheme(this.currentTheme);
        
        // 初始化主题切换按钮
        this.initThemeToggle();
        
        // 监听系统主题变化
        this.watchSystemTheme();
        
        console.log(`主题管理器已初始化，当前主题: ${this.currentTheme}`);
    }
    
    initThemeToggle() {
        // 创建主题切换按钮
        const themeToggle = document.querySelector('.theme-toggle');
        
        if (themeToggle) {
            // 设置初始图标
            this.updateToggleIcon(themeToggle);
            
            // 绑定点击事件
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
            
            // 添加键盘支持
            themeToggle.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleTheme();
                }
            });
        }
        
        // 如果没有找到切换按钮，创建一个
        if (!themeToggle) {
            this.createThemeToggle();
        }
    }
    
    createThemeToggle() {
        const navbar = document.querySelector('.navbar-menu .navbar-end');
        if (navbar) {
            const themeToggleContainer = document.createElement('div');
            themeToggleContainer.className = 'navbar-item';
            
            const themeToggle = document.createElement('button');
            themeToggle.className = 'theme-toggle';
            themeToggle.setAttribute('aria-label', '切换主题');
            themeToggle.setAttribute('title', '切换明暗主题');
            
            this.updateToggleIcon(themeToggle);
            
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
            
            themeToggleContainer.appendChild(themeToggle);
            navbar.appendChild(themeToggleContainer);
        }
    }
    
    updateToggleIcon(toggleButton) {
        const icon = this.currentTheme === 'light' ? 'fa-moon' : 'fa-sun';
        const title = this.currentTheme === 'light' ? '切换到暗主题' : '切换到明主题';
        
        toggleButton.innerHTML = `<i class="fas ${icon}"></i>`;
        toggleButton.setAttribute('title', title);
        toggleButton.setAttribute('aria-label', title);
    }
    
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }
    
    setTheme(theme) {
        if (theme !== 'light' && theme !== 'dark') {
            console.warn(`无效的主题: ${theme}`);
            return;
        }
        
        this.currentTheme = theme;
        this.applyTheme(theme);
        this.saveTheme(theme);
        
        // 更新切换按钮图标
        const themeToggle = document.querySelector('.theme-toggle');
        if (themeToggle) {
            this.updateToggleIcon(themeToggle);
        }
        
        // 触发主题变化事件
        this.dispatchThemeChangeEvent(theme);
        
        console.log(`主题已切换到: ${theme}`);
    }
    
    applyTheme(theme) {
        // 设置HTML的data-theme属性
        document.documentElement.setAttribute('data-theme', theme);
        
        // 为了兼容性，也设置body的class
        document.body.classList.remove('theme-light', 'theme-dark');
        document.body.classList.add(`theme-${theme}`);
        
        // 更新meta标签的theme-color
        this.updateThemeColor(theme);
    }
    
    updateThemeColor(theme) {
        let themeColor = '#3273dc'; // 默认主色调
        
        if (theme === 'dark') {
            themeColor = '#1a1a1a'; // 暗主题背景色
        }
        
        // 更新或创建theme-color meta标签
        let themeColorMeta = document.querySelector('meta[name="theme-color"]');
        if (!themeColorMeta) {
            themeColorMeta = document.createElement('meta');
            themeColorMeta.name = 'theme-color';
            document.head.appendChild(themeColorMeta);
        }
        themeColorMeta.content = themeColor;
    }
    
    saveTheme(theme) {
        try {
            localStorage.setItem(this.themeKey, theme);
        } catch (e) {
            console.warn('无法保存主题设置到localStorage:', e);
        }
    }
    
    watchSystemTheme() {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        
        mediaQuery.addEventListener('change', (e) => {
            // 只有在用户没有手动设置主题时才跟随系统
            const savedTheme = localStorage.getItem(this.themeKey);
            if (!savedTheme) {
                const systemTheme = e.matches ? 'dark' : 'light';
                this.setTheme(systemTheme);
            }
        });
    }
    
    dispatchThemeChangeEvent(theme) {
        const event = new CustomEvent('themechange', {
            detail: { theme: theme }
        });
        document.dispatchEvent(event);
    }
    
    // 获取当前主题
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    // 检查是否为暗主题
    isDarkTheme() {
        return this.currentTheme === 'dark';
    }
    
    // 重置主题到系统默认
    resetToSystemTheme() {
        localStorage.removeItem(this.themeKey);
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        const systemTheme = systemPrefersDark ? 'dark' : 'light';
        this.setTheme(systemTheme);
    }
}

// 主题切换动画效果
function addThemeTransition() {
    const css = `
        * {
            transition: background-color 0.3s ease, 
                       color 0.3s ease, 
                       border-color 0.3s ease,
                       box-shadow 0.3s ease !important;
        }
        
        .theme-transition-disable * {
            transition: none !important;
        }
    `;
    
    const style = document.createElement('style');
    style.textContent = css;
    document.head.appendChild(style);
}

// 主题切换时的特殊效果
function addThemeChangeEffect() {
    document.addEventListener('themechange', function(e) {
        // 添加切换效果类
        document.body.classList.add('theme-changing');
        
        // 创建切换动画
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: ${e.detail.theme === 'dark' ? '#000' : '#fff'};
            opacity: 0;
            pointer-events: none;
            z-index: 9999;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(overlay);
        
        // 触发动画
        requestAnimationFrame(() => {
            overlay.style.opacity = '0.3';
            
            setTimeout(() => {
                overlay.style.opacity = '0';
                
                setTimeout(() => {
                    document.body.removeChild(overlay);
                    document.body.classList.remove('theme-changing');
                }, 300);
            }, 150);
        });
    });
}

// 初始化主题管理器
let themeManager;

document.addEventListener('DOMContentLoaded', function() {
    // 添加主题切换动画
    addThemeTransition();
    addThemeChangeEffect();
    
    // 初始化主题管理器
    themeManager = new ThemeManager();
    
    // 将主题管理器暴露到全局，方便其他脚本使用
    window.themeManager = themeManager;
});

// 导出主题管理器类（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}

// 视频播放器功能

class VideoPlayer {
    constructor() {
        this.currentVideo = null;
        this.videoModal = null;
        this.isPlaying = false;
        this.currentTime = 0;
        this.duration = 0;
        
        this.init();
    }
    
    init() {
        this.createVideoModal();
        this.bindVideoEvents();
        this.bindFilterEvents();
        
        console.log('视频播放器功能已初始化');
    }
    
    createVideoModal() {
        // 创建视频模态框
        const modalHTML = `
            <div class="modal" id="video-modal">
                <div class="modal-background"></div>
                <div class="modal-content">
                    <div class="video-player-container">
                        <video id="modal-video" controls preload="metadata">
                            <source src="" type="video/mp4">
                            您的浏览器不支持视频播放。
                        </video>
                        <div class="video-info">
                            <h3 class="video-title"></h3>
                            <div class="video-meta">
                                <span class="video-views"></span>
                                <span class="video-date"></span>
                            </div>
                            <div class="video-description"></div>
                        </div>
                    </div>
                </div>
                <button class="modal-close is-large" aria-label="关闭"></button>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        this.videoModal = document.getElementById('video-modal');
        this.modalVideo = document.getElementById('modal-video');
        
        // 绑定关闭事件
        const closeBtn = this.videoModal.querySelector('.modal-close');
        const background = this.videoModal.querySelector('.modal-background');
        
        closeBtn.addEventListener('click', () => this.closeVideoModal());
        background.addEventListener('click', () => this.closeVideoModal());
        
        // 绑定键盘事件
        document.addEventListener('keydown', (e) => {
            if (this.videoModal.classList.contains('is-active')) {
                if (e.key === 'Escape') {
                    this.closeVideoModal();
                } else if (e.key === ' ') {
                    e.preventDefault();
                    this.togglePlay();
                }
            }
        });
    }
    
    bindVideoEvents() {
        // 绑定视频卡片点击事件
        const videoItems = document.querySelectorAll('.video-item');
        videoItems.forEach(item => {
            const playButton = item.querySelector('.play-button');
            const videoLink = item.querySelector('.title a');
            
            if (playButton) {
                playButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.playVideo(item);
                });
            }
            
            if (videoLink) {
                videoLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.playVideo(item);
                });
            }
        });
        
        // 绑定推荐视频点击事件
        const recommendedVideos = document.querySelectorAll('.media .title a');
        recommendedVideos.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                // 这里可以根据链接获取视频信息
                this.playVideoById(this.getVideoIdFromUrl(link.href));
            });
        });
    }
    
    bindFilterEvents() {
        // 绑定筛选功能
        const filterOptions = document.querySelectorAll('.filter-option');
        filterOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleFilterClick(option);
            });
        });
        
        // 绑定加载更多按钮
        const loadMoreBtn = document.getElementById('load-more-videos');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreVideos();
            });
        }
    }
    
    playVideo(videoItem) {
        // 获取视频信息
        const title = videoItem.querySelector('.title a').textContent;
        const description = videoItem.querySelector('.content').textContent;
        const views = videoItem.querySelector('.fa-eye').nextElementSibling.textContent;
        const date = videoItem.querySelector('.fa-calendar').nextElementSibling.textContent;
        const thumbnail = videoItem.querySelector('img').src;
        
        // 模拟视频URL（实际项目中应该从数据源获取）
        const videoUrl = this.getVideoUrl(videoItem);
        
        this.openVideoModal({
            title: title,
            description: description,
            views: views,
            date: date,
            thumbnail: thumbnail,
            url: videoUrl
        });
    }
    
    playVideoById(videoId) {
        // 根据视频ID播放视频（实际项目中从API获取视频信息）
        const videoData = this.getVideoDataById(videoId);
        if (videoData) {
            this.openVideoModal(videoData);
        }
    }
    
    openVideoModal(videoData) {
        if (!this.videoModal) return;
        
        // 设置视频信息
        this.videoModal.querySelector('.video-title').textContent = videoData.title;
        this.videoModal.querySelector('.video-views').textContent = `${videoData.views} 次观看`;
        this.videoModal.querySelector('.video-date').textContent = videoData.date;
        this.videoModal.querySelector('.video-description').textContent = videoData.description;
        
        // 设置视频源
        if (videoData.url) {
            this.modalVideo.src = videoData.url;
        } else {
            // 如果没有实际视频，显示缩略图
            this.modalVideo.poster = videoData.thumbnail;
        }
        
        // 显示模态框
        this.videoModal.classList.add('is-active');
        document.body.classList.add('modal-open');
        
        // 记录当前视频
        this.currentVideo = videoData;
    }
    
    closeVideoModal() {
        if (!this.videoModal) return;
        
        // 暂停视频
        if (this.modalVideo) {
            this.modalVideo.pause();
            this.modalVideo.currentTime = 0;
        }
        
        // 隐藏模态框
        this.videoModal.classList.remove('is-active');
        document.body.classList.remove('modal-open');
        
        this.currentVideo = null;
    }
    
    togglePlay() {
        if (!this.modalVideo) return;
        
        if (this.modalVideo.paused) {
            this.modalVideo.play();
        } else {
            this.modalVideo.pause();
        }
    }
    
    handleFilterClick(option) {
        const filterGroup = option.closest('.filter-group');
        const filterType = this.getFilterType(filterGroup);
        const filterValue = option.getAttribute(`data-${filterType}`);
        
        // 更新活动状态
        filterGroup.querySelectorAll('.filter-option').forEach(opt => {
            opt.classList.remove('is-active');
        });
        option.classList.add('is-active');
        
        // 应用筛选
        this.applyVideoFilter(filterType, filterValue);
    }
    
    getFilterType(filterGroup) {
        const label = filterGroup.querySelector('.filter-label').textContent;
        switch (label) {
            case '视频分类': return 'category';
            case '产品类型': return 'product';
            case '排序方式': return 'sort';
            default: return 'category';
        }
    }
    
    applyVideoFilter(filterType, filterValue) {
        const videoItems = document.querySelectorAll('.video-item');
        
        videoItems.forEach(item => {
            let shouldShow = true;
            
            // 检查筛选条件
            if (filterValue !== 'all') {
                const itemValue = item.getAttribute(`data-${filterType}`);
                if (itemValue !== filterValue) {
                    shouldShow = false;
                }
            }
            
            // 显示或隐藏视频项
            if (shouldShow) {
                item.style.display = 'block';
                item.classList.add('fade-in');
            } else {
                item.style.display = 'none';
                item.classList.remove('fade-in');
            }
        });
        
        // 更新结果计数
        this.updateVideoCount();
    }
    
    updateVideoCount() {
        const visibleVideos = document.querySelectorAll('.video-item[style*="block"], .video-item:not([style*="none"])');
        const totalCount = visibleVideos.length;
        
        // 创建或更新结果统计
        let resultsInfo = document.querySelector('.video-results-info');
        if (!resultsInfo) {
            resultsInfo = document.createElement('div');
            resultsInfo.className = 'video-results-info has-text-centered mb-4';
            const grid = document.getElementById('videos-grid');
            grid.parentNode.insertBefore(resultsInfo, grid);
        }
        
        resultsInfo.innerHTML = `<p class="has-text-grey">找到 ${totalCount} 个视频</p>`;
    }
    
    loadMoreVideos() {
        const loadMoreBtn = document.getElementById('load-more-videos');
        const originalText = loadMoreBtn.innerHTML;
        
        loadMoreBtn.innerHTML = '<span class="loading-spinner"></span> 加载中...';
        loadMoreBtn.disabled = true;
        
        // 模拟加载更多视频
        setTimeout(() => {
            loadMoreBtn.innerHTML = originalText;
            loadMoreBtn.disabled = false;
            
            showNotification('暂无更多视频', 'info');
        }, 1000);
    }
    
    getVideoUrl(videoItem) {
        // 实际项目中应该从数据源获取真实的视频URL
        // 这里返回示例视频或null
        return null; // 暂时返回null，使用缩略图作为占位
    }
    
    getVideoIdFromUrl(url) {
        // 从URL中提取视频ID
        const match = url.match(/id=(\d+)/);
        return match ? match[1] : null;
    }
    
    getVideoDataById(videoId) {
        // 模拟从API获取视频数据
        const videoDatabase = {
            '1': {
                title: '铝合金门窗安装技巧',
                description: '专业的铝合金门窗安装工艺展示，从测量定位到最终验收的完整流程。',
                views: '5,230',
                date: '2024-01-08',
                thumbnail: 'assets/images/videos/video-thumb-1.jpg',
                url: null
            },
            '2': {
                title: '北京金融中心项目回顾',
                description: '记录北京国际金融中心玻璃幕墙项目从设计到完工的全过程。',
                views: '8,960',
                date: '2024-01-05',
                thumbnail: 'assets/images/videos/video-thumb-2.jpg',
                url: null
            },
            '3': {
                title: '节能玻璃技术解析',
                description: '详细介绍Low-E玻璃、中空玻璃等节能技术的原理和应用。',
                views: '3,450',
                date: '2024-01-03',
                thumbnail: 'assets/images/videos/video-thumb-3.jpg',
                url: null
            }
        };
        
        return videoDatabase[videoId] || null;
    }
    
    // 添加视频播放统计
    trackVideoPlay(videoData) {
        // 实际项目中可以发送统计数据到服务器
        console.log('视频播放统计:', videoData.title);
    }
    
    // 添加视频收藏功能
    toggleFavorite(videoId) {
        // 实际项目中可以保存到用户收藏列表
        const favorites = JSON.parse(localStorage.getItem('video-favorites') || '[]');
        const index = favorites.indexOf(videoId);
        
        if (index > -1) {
            favorites.splice(index, 1);
            showNotification('已取消收藏', 'info');
        } else {
            favorites.push(videoId);
            showNotification('已添加到收藏', 'success');
        }
        
        localStorage.setItem('video-favorites', JSON.stringify(favorites));
    }
}

// 初始化视频播放器
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('videos-grid')) {
        window.videoPlayer = new VideoPlayer();
    }
});

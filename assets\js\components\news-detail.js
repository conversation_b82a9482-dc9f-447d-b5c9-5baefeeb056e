// 资讯详情页功能

class NewsDetail {
    constructor() {
        this.newsId = null;
        this.isBookmarked = false;
        this.readingProgress = 0;
        this.tocItems = [];
        
        this.init();
    }
    
    init() {
        this.getNewsId();
        this.bindInteractionEvents();
        this.bindCommentEvents();
        this.generateTOC();
        this.trackReadingProgress();
        this.loadNewsData();
        
        console.log('资讯详情页功能已初始化');
    }
    
    getNewsId() {
        const urlParams = new URLSearchParams(window.location.search);
        this.newsId = urlParams.get('id') || '1';
    }
    
    bindInteractionEvents() {
        // 收藏按钮
        const bookmarkBtn = document.getElementById('bookmark-btn');
        if (bookmarkBtn) {
            bookmarkBtn.addEventListener('click', () => {
                this.toggleBookmark();
            });
        }
        
        // 分享按钮
        const shareBtn = document.getElementById('share-btn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => {
                this.shareArticle();
            });
        }
        
        // 目录导航
        const tocLinks = document.querySelectorAll('.toc a');
        tocLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.scrollToSection(link.getAttribute('href'));
            });
        });
    }
    
    bindCommentEvents() {
        // 评论表单提交
        const commentForm = document.querySelector('.comment-form form');
        if (commentForm) {
            commentForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitComment(commentForm);
            });
        }
        
        // 回复按钮
        const replyButtons = document.querySelectorAll('.comment .level-item[aria-label="回复"]');
        replyButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showReplyForm(btn.closest('.comment'));
            });
        });
        
        // 点赞按钮
        const likeButtons = document.querySelectorAll('.comment .level-item[aria-label="点赞"]');
        likeButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleCommentLike(btn);
            });
        });
        
        // 加载更多评论
        const loadMoreBtn = document.getElementById('load-more-comments');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreComments();
            });
        }
    }
    
    generateTOC() {
        // 自动生成目录
        const headings = document.querySelectorAll('.article-body h2, .article-body h3');
        const tocContainer = document.querySelector('.toc ul');
        
        if (!tocContainer || headings.length === 0) return;
        
        // 清空现有目录
        tocContainer.innerHTML = '';
        
        headings.forEach((heading, index) => {
            const id = heading.textContent.replace(/\s+/g, '');
            heading.id = id;
            
            const li = document.createElement('li');
            const a = document.createElement('a');
            a.href = `#${id}`;
            a.textContent = heading.textContent;
            
            if (heading.tagName === 'H3') {
                li.style.marginLeft = '20px';
            }
            
            a.addEventListener('click', (e) => {
                e.preventDefault();
                this.scrollToSection(`#${id}`);
            });
            
            li.appendChild(a);
            tocContainer.appendChild(li);
            
            this.tocItems.push({
                element: heading,
                link: a,
                id: id
            });
        });
    }
    
    trackReadingProgress() {
        // 跟踪阅读进度
        const articleBody = document.querySelector('.article-body');
        if (!articleBody) return;
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const rect = entry.boundingClientRect;
                    const progress = Math.max(0, Math.min(100, 
                        ((window.innerHeight - rect.top) / window.innerHeight) * 100
                    ));
                    this.updateReadingProgress(progress);
                }
            });
        }, {
            threshold: [0, 0.25, 0.5, 0.75, 1]
        });
        
        observer.observe(articleBody);
        
        // 高亮当前阅读位置的目录项
        window.addEventListener('scroll', () => {
            this.updateTOCHighlight();
        });
    }
    
    updateReadingProgress(progress) {
        this.readingProgress = progress;
        
        // 创建或更新进度条
        let progressBar = document.querySelector('.reading-progress');
        if (!progressBar) {
            progressBar = document.createElement('div');
            progressBar.className = 'reading-progress';
            progressBar.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 0%;
                height: 3px;
                background: #3273dc;
                z-index: 1000;
                transition: width 0.3s ease;
            `;
            document.body.appendChild(progressBar);
        }
        
        progressBar.style.width = `${progress}%`;
    }
    
    updateTOCHighlight() {
        // 高亮当前可见的目录项
        this.tocItems.forEach(item => {
            const rect = item.element.getBoundingClientRect();
            if (rect.top <= 100 && rect.bottom >= 100) {
                // 移除所有高亮
                this.tocItems.forEach(tocItem => {
                    tocItem.link.classList.remove('is-active');
                });
                // 添加当前高亮
                item.link.classList.add('is-active');
            }
        });
    }
    
    scrollToSection(target) {
        const element = document.querySelector(target);
        if (element) {
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
    
    toggleBookmark() {
        const bookmarkBtn = document.getElementById('bookmark-btn');
        const bookmarkIcon = bookmarkBtn.querySelector('i');
        const bookmarkText = bookmarkBtn.querySelector('span:last-child');
        
        if (this.isBookmarked) {
            // 取消收藏
            this.isBookmarked = false;
            bookmarkBtn.classList.remove('is-primary');
            bookmarkIcon.classList.remove('fas');
            bookmarkIcon.classList.add('far');
            bookmarkText.textContent = '收藏';
            showNotification('已取消收藏', 'info');
        } else {
            // 收藏
            this.isBookmarked = true;
            bookmarkBtn.classList.add('is-primary');
            bookmarkIcon.classList.remove('far');
            bookmarkIcon.classList.add('fas');
            bookmarkText.textContent = '已收藏';
            showNotification('已添加到收藏夹', 'success');
        }
        
        // 保存到本地存储
        this.saveBookmarkState();
    }
    
    shareArticle() {
        const articleUrl = window.location.href;
        const articleTitle = document.querySelector('.article-header .title').textContent;
        
        // 检查是否支持Web Share API
        if (navigator.share) {
            navigator.share({
                title: articleTitle,
                text: '分享一篇有趣的建筑技术文章',
                url: articleUrl
            }).then(() => {
                showNotification('分享成功', 'success');
            }).catch((error) => {
                console.log('分享失败:', error);
                this.fallbackShare(articleUrl, articleTitle);
            });
        } else {
            this.fallbackShare(articleUrl, articleTitle);
        }
    }
    
    fallbackShare(url, title) {
        // 复制链接到剪贴板
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(() => {
                showNotification('链接已复制到剪贴板', 'success');
            }).catch(() => {
                this.showShareModal(url, title);
            });
        } else {
            this.showShareModal(url, title);
        }
    }
    
    showShareModal(url, title) {
        // 创建分享模态框
        const modalHTML = `
            <div class="modal is-active" id="share-modal">
                <div class="modal-background"></div>
                <div class="modal-card">
                    <header class="modal-card-head">
                        <p class="modal-card-title">分享文章</p>
                        <button class="delete" aria-label="关闭"></button>
                    </header>
                    <section class="modal-card-body">
                        <div class="field">
                            <label class="label">文章链接</label>
                            <div class="control">
                                <input class="input" type="text" value="${url}" readonly>
                            </div>
                        </div>
                        <div class="buttons is-centered">
                            <button class="button is-primary" onclick="this.previousElementSibling.previousElementSibling.querySelector('input').select(); document.execCommand('copy'); showNotification('链接已复制', 'success');">
                                复制链接
                            </button>
                        </div>
                    </section>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        const modal = document.getElementById('share-modal');
        const deleteBtn = modal.querySelector('.delete');
        const background = modal.querySelector('.modal-background');
        
        deleteBtn.addEventListener('click', () => modal.remove());
        background.addEventListener('click', () => modal.remove());
    }
    
    submitComment(form) {
        const formData = new FormData(form);
        const name = form.querySelector('input[type="text"]').value;
        const email = form.querySelector('input[type="email"]').value;
        const content = form.querySelector('textarea').value;
        
        if (!name || !content) {
            showNotification('请填写必要信息', 'warning');
            return;
        }
        
        // 模拟提交评论
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.innerHTML = '<span class="loading-spinner"></span> 提交中...';
        submitBtn.disabled = true;
        
        setTimeout(() => {
            // 添加新评论到列表
            this.addCommentToList({
                name: name,
                content: content,
                time: new Date().toLocaleString('zh-CN')
            });
            
            // 重置表单
            form.reset();
            
            // 恢复按钮
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            
            showNotification('评论发表成功', 'success');
        }, 1000);
    }
    
    addCommentToList(commentData) {
        const commentsList = document.querySelector('.comments-list');
        const commentsTitle = commentsList.querySelector('.title');
        
        // 更新评论数量
        const currentCount = parseInt(commentsTitle.textContent.match(/\d+/)[0]);
        commentsTitle.textContent = `评论 (${currentCount + 1})`;
        
        // 创建新评论HTML
        const commentHTML = `
            <article class="comment">
                <div class="media">
                    <figure class="media-left">
                        <p class="image is-48x48">
                            <img class="is-rounded" src="assets/images/avatars/default.jpg" alt="用户头像">
                        </p>
                    </figure>
                    <div class="media-content">
                        <div class="content">
                            <p>
                                <strong>${commentData.name}</strong>
                                <small class="has-text-grey">${commentData.time}</small>
                                <br>
                                ${commentData.content}
                            </p>
                        </div>
                        <nav class="level is-mobile">
                            <div class="level-left">
                                <a class="level-item" aria-label="回复">
                                    <span class="icon is-small">
                                        <i class="fas fa-reply"></i>
                                    </span>
                                    <span>回复</span>
                                </a>
                                <a class="level-item" aria-label="点赞">
                                    <span class="icon is-small">
                                        <i class="fas fa-thumbs-up"></i>
                                    </span>
                                    <span>0</span>
                                </a>
                            </div>
                        </nav>
                    </div>
                </div>
            </article>
        `;
        
        // 插入到评论列表顶部
        const firstComment = commentsList.querySelector('.comment');
        if (firstComment) {
            firstComment.insertAdjacentHTML('beforebegin', commentHTML);
        } else {
            commentsTitle.insertAdjacentHTML('afterend', commentHTML);
        }
        
        // 重新绑定事件
        this.bindCommentEvents();
    }
    
    showReplyForm(commentElement) {
        // 显示回复表单
        const existingForm = commentElement.querySelector('.reply-form');
        if (existingForm) {
            existingForm.remove();
            return;
        }
        
        const replyFormHTML = `
            <div class="reply-form mt-3">
                <form>
                    <div class="field">
                        <div class="control">
                            <textarea class="textarea is-small" placeholder="写下您的回复..." rows="3" required></textarea>
                        </div>
                    </div>
                    <div class="field is-grouped">
                        <div class="control">
                            <button class="button is-primary is-small" type="submit">回复</button>
                        </div>
                        <div class="control">
                            <button class="button is-light is-small" type="button" onclick="this.closest('.reply-form').remove()">取消</button>
                        </div>
                    </div>
                </form>
            </div>
        `;
        
        commentElement.insertAdjacentHTML('beforeend', replyFormHTML);
        
        // 绑定回复表单事件
        const replyForm = commentElement.querySelector('.reply-form form');
        replyForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const content = replyForm.querySelector('textarea').value;
            if (content.trim()) {
                showNotification('回复发表成功', 'success');
                replyForm.closest('.reply-form').remove();
            }
        });
    }
    
    toggleCommentLike(likeBtn) {
        const countSpan = likeBtn.querySelector('span:last-child');
        const icon = likeBtn.querySelector('i');
        let count = parseInt(countSpan.textContent);
        
        if (icon.classList.contains('fas')) {
            // 取消点赞
            icon.classList.remove('fas');
            icon.classList.add('far');
            count--;
            likeBtn.classList.remove('has-text-primary');
        } else {
            // 点赞
            icon.classList.remove('far');
            icon.classList.add('fas');
            count++;
            likeBtn.classList.add('has-text-primary');
        }
        
        countSpan.textContent = count;
    }
    
    loadMoreComments() {
        const loadMoreBtn = document.getElementById('load-more-comments');
        const originalText = loadMoreBtn.innerHTML;
        
        loadMoreBtn.innerHTML = '<span class="loading-spinner"></span> 加载中...';
        loadMoreBtn.disabled = true;
        
        setTimeout(() => {
            loadMoreBtn.innerHTML = originalText;
            loadMoreBtn.disabled = false;
            showNotification('暂无更多评论', 'info');
        }, 1000);
    }
    
    loadNewsData() {
        // 根据资讯ID加载资讯数据
        const newsData = this.getNewsDataById(this.newsId);
        if (newsData) {
            this.updateNewsInfo(newsData);
        }
        
        // 加载用户交互状态
        this.loadUserInteractions();
        
        // 记录阅读历史
        this.addToReadingHistory();
    }
    
    updateNewsInfo(newsData) {
        // 更新页面标题
        document.title = `${newsData.title} - 资讯详情 - 建筑科技公司`;
        
        // 更新meta描述
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
            metaDescription.content = newsData.description;
        }
    }
    
    getNewsDataById(newsId) {
        // 模拟资讯数据库
        const newsDatabase = {
            '1': {
                title: '绿色建筑技术发展趋势分析',
                description: '深入探讨绿色建筑技术的发展现状、技术特点和未来发展方向',
                category: 'industry',
                date: '2024-01-18'
            }
        };
        
        return newsDatabase[newsId] || newsDatabase['1'];
    }
    
    saveBookmarkState() {
        const bookmarks = JSON.parse(localStorage.getItem('news-bookmarks') || '[]');
        const index = bookmarks.indexOf(this.newsId);
        
        if (this.isBookmarked && index === -1) {
            bookmarks.push(this.newsId);
        } else if (!this.isBookmarked && index > -1) {
            bookmarks.splice(index, 1);
        }
        
        localStorage.setItem('news-bookmarks', JSON.stringify(bookmarks));
    }
    
    loadUserInteractions() {
        // 加载收藏状态
        const bookmarks = JSON.parse(localStorage.getItem('news-bookmarks') || '[]');
        if (bookmarks.includes(this.newsId)) {
            this.isBookmarked = true;
            const bookmarkBtn = document.getElementById('bookmark-btn');
            if (bookmarkBtn) {
                bookmarkBtn.classList.add('is-primary');
                bookmarkBtn.querySelector('i').classList.remove('far');
                bookmarkBtn.querySelector('i').classList.add('fas');
                bookmarkBtn.querySelector('span:last-child').textContent = '已收藏';
            }
        }
    }
    
    addToReadingHistory() {
        const title = document.querySelector('.article-header .title').textContent;
        const history = JSON.parse(localStorage.getItem('reading-history') || '[]');
        
        const historyItem = {
            id: this.newsId,
            title: title,
            timestamp: new Date().toISOString()
        };
        
        const existingIndex = history.findIndex(item => item.id === this.newsId);
        if (existingIndex > -1) {
            history[existingIndex] = historyItem;
        } else {
            history.unshift(historyItem);
            if (history.length > 50) {
                history.pop();
            }
        }
        
        localStorage.setItem('reading-history', JSON.stringify(history));
    }
}

// 初始化资讯详情页功能
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.article-content')) {
        window.newsDetail = new NewsDetail();
    }
});

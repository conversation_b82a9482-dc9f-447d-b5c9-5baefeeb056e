// 建筑科技公司官网变量定义
// 基于 Bulma 变量系统

// 颜色定义
$primary: #3273dc;           // 主色调 - Bulma 默认蓝色，代表科技感
$primary-light: #5a8def;     // 主色调浅色
$primary-dark: #2366d1;      // 主色调深色

$secondary: #363636;         // 次要颜色 - 深灰色
$secondary-light: #4a4a4a;   // 次要颜色浅色
$secondary-dark: #2b2b2b;    // 次要颜色深色

$accent: #00d1b2;           // 强调色 - Bulma 青色
$accent-light: #26d9c4;     // 强调色浅色
$accent-dark: #00b89c;      // 强调色深色

// 灰度色彩
$white: #ffffff;
$white-ter: #f5f5f5;
$white-bis: #fafafa;
$grey-lightest: #f8f9fa;
$grey-lighter: #e9ecef;
$grey-light: #dee2e6;
$grey: #6c757d;
$grey-dark: #495057;
$grey-darker: #343a40;
$black-ter: #242424;
$black-bis: #121212;
$black: #000000;

// 状态颜色
$success: #48c774;          // 成功色
$warning: #ffdd57;          // 警告色
$danger: #f14668;           // 危险色
$info: #3298dc;             // 信息色

// 字体定义
$family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
$family-monospace: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
$family-primary: $family-sans-serif;

// 字体大小
$size-1: 3rem;              // 48px
$size-2: 2.5rem;            // 40px
$size-3: 2rem;              // 32px
$size-4: 1.5rem;            // 24px
$size-5: 1.25rem;           // 20px
$size-6: 1rem;              // 16px
$size-7: 0.875rem;          // 14px

// 字重
$weight-light: 300;
$weight-normal: 400;
$weight-medium: 500;
$weight-semibold: 600;
$weight-bold: 700;

// 间距
$spacing-xs: 0.25rem;       // 4px
$spacing-sm: 0.5rem;        // 8px
$spacing-md: 1rem;          // 16px
$spacing-lg: 1.5rem;        // 24px
$spacing-xl: 2rem;          // 32px
$spacing-xxl: 3rem;         // 48px

// 圆角
$radius-small: 2px;
$radius: 4px;
$radius-large: 6px;
$radius-rounded: 290486px;

// 阴影
$shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
$shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
$shadow-medium: 0 8px 16px rgba(0, 0, 0, 0.15);
$shadow-large: 0 16px 32px rgba(0, 0, 0, 0.15);

// 断点
$mobile: 768px;
$tablet: 769px;
$desktop: 1024px;
$widescreen: 1216px;
$fullhd: 1408px;

// 容器宽度
$container-max-width: 1200px;

// 导航栏
$navbar-height: 3.25rem;    // 52px
$navbar-background-color: $white;
$navbar-box-shadow-size: 0 2px 0 0;
$navbar-box-shadow-color: $grey-lighter;

// 页脚
$footer-background-color: $grey-darker;
$footer-color: $white-ter;
$footer-padding: 3rem 1.5rem 6rem;

// 卡片
$card-shadow: $shadow;
$card-radius: $radius;
$card-background-color: $white;
$card-color: $grey-darker;

// 按钮
$button-border-radius: $radius;
$button-padding-vertical: 0.5em;
$button-padding-horizontal: 1em;

// 表单
$input-border-radius: $radius;
$input-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);

// 动画时长
$duration-short: 0.15s;
$duration-medium: 0.3s;
$duration-long: 0.6s;

// Z-index 层级
$z-dropdown: 20;
$z-sticky: 30;
$z-fixed: 40;
$z-modal-background: 40;
$z-modal: 50;
$z-notification: 60;
$z-tooltip: 70;

// 主题相关变量
$theme-transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;

// 明主题变量
$light-background: $white;
$light-surface: $white-ter;
$light-text: $grey-darker;
$light-text-light: $grey;
$light-border: $grey-lighter;

// 暗主题变量
$dark-background: #1a1a1a;
$dark-surface: #2d2d2d;
$dark-text: #e0e0e0;
$dark-text-light: #b0b0b0;
$dark-border: #404040;

// 公司品牌相关
$brand-primary: $primary;
$brand-secondary: $secondary;
$brand-gradient: linear-gradient(135deg, $primary 0%, $accent 100%);

// 特殊效果
$glass-effect: rgba(255, 255, 255, 0.1);
$glass-border: rgba(255, 255, 255, 0.2);
$backdrop-blur: blur(10px);

// 内容区域
$content-max-width: 800px;
$hero-min-height: 100vh;
$section-padding: 3rem 1.5rem;

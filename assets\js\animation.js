// GSAP动画效果实现

// 检查GSAP是否已加载
if (typeof gsap === 'undefined') {
    console.warn('GSAP未加载，动画效果将不可用');
} else {
    // 注册GSAP插件
    gsap.registerPlugin(ScrollTrigger);
    
    // 初始化动画
    document.addEventListener('DOMContentLoaded', function() {
        initializeGSAPAnimations();
    });
}

function initializeGSAPAnimations() {
    // 页面加载动画
    initPageLoadAnimation();
    
    // 滚动触发动画
    initScrollAnimations();
    
    // Hero区域动画
    initHeroAnimations();
    
    // 数字计数动画
    initCounterAnimations();
    
    // 视差效果
    initParallaxEffects();
    
    // 悬停动画
    initHoverAnimations();
    
    // 页面切换动画
    initPageTransitions();
    
    console.log('GSAP动画已初始化完成');
}

// 页面加载动画
function initPageLoadAnimation() {
    const tl = gsap.timeline();
    
    // 导航栏入场
    tl.from('.navbar', {
        y: -100,
        opacity: 0,
        duration: 0.8,
        ease: 'power2.out'
    });
    
    // Hero内容入场
    tl.from('.hero .title', {
        y: 50,
        opacity: 0,
        duration: 0.8,
        ease: 'power2.out'
    }, '-=0.4');
    
    tl.from('.hero .subtitle', {
        y: 30,
        opacity: 0,
        duration: 0.6,
        ease: 'power2.out'
    }, '-=0.4');
    
    tl.from('.hero .button', {
        y: 20,
        opacity: 0,
        duration: 0.6,
        ease: 'power2.out',
        stagger: 0.1
    }, '-=0.3');
}

// 滚动触发动画
function initScrollAnimations() {
    // 淡入动画
    gsap.utils.toArray('.fade-in').forEach(element => {
        gsap.from(element, {
            opacity: 0,
            y: 50,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    // 左侧滑入
    gsap.utils.toArray('.slide-in-left').forEach(element => {
        gsap.from(element, {
            opacity: 0,
            x: -100,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    // 右侧滑入
    gsap.utils.toArray('.slide-in-right').forEach(element => {
        gsap.from(element, {
            opacity: 0,
            x: 100,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    // 卡片交错动画
    gsap.utils.toArray('.card').forEach((card, index) => {
        gsap.from(card, {
            opacity: 0,
            y: 50,
            duration: 0.6,
            ease: 'power2.out',
            delay: index * 0.1,
            scrollTrigger: {
                trigger: card,
                start: 'top 85%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    // 标题动画
    gsap.utils.toArray('.title, .subtitle').forEach(title => {
        gsap.from(title, {
            opacity: 0,
            y: 30,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: title,
                start: 'top 85%',
                toggleActions: 'play none none reverse'
            }
        });
    });
}

// Hero区域特殊动画
function initHeroAnimations() {
    const heroSection = document.querySelector('.hero');
    if (!heroSection) return;
    
    // 背景视差效果
    const heroBackground = heroSection.querySelector('.hero-background');
    if (heroBackground) {
        gsap.to(heroBackground, {
            yPercent: -50,
            ease: 'none',
            scrollTrigger: {
                trigger: heroSection,
                start: 'top bottom',
                end: 'bottom top',
                scrub: true
            }
        });
    }
    
    // Hero内容视差
    const heroContent = heroSection.querySelector('.hero-content');
    if (heroContent) {
        gsap.to(heroContent, {
            yPercent: -30,
            ease: 'none',
            scrollTrigger: {
                trigger: heroSection,
                start: 'top bottom',
                end: 'bottom top',
                scrub: true
            }
        });
    }
    
    // 轮播指示器动画
    const indicators = heroSection.querySelectorAll('.indicator');
    indicators.forEach(indicator => {
        indicator.addEventListener('mouseenter', () => {
            gsap.to(indicator, {
                scale: 1.2,
                duration: 0.3,
                ease: 'power2.out'
            });
        });
        
        indicator.addEventListener('mouseleave', () => {
            gsap.to(indicator, {
                scale: 1,
                duration: 0.3,
                ease: 'power2.out'
            });
        });
    });
}

// 数字计数动画
function initCounterAnimations() {
    gsap.utils.toArray('.counter-number').forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target')) || 0;
        
        gsap.to(counter, {
            textContent: target,
            duration: 2,
            ease: 'power2.out',
            snap: { textContent: 1 },
            scrollTrigger: {
                trigger: counter,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
}

// 视差效果
function initParallaxEffects() {
    // 通用视差元素
    gsap.utils.toArray('.parallax-element').forEach(element => {
        const speed = element.getAttribute('data-speed') || 0.5;
        
        gsap.to(element, {
            yPercent: -100 * speed,
            ease: 'none',
            scrollTrigger: {
                trigger: element,
                start: 'top bottom',
                end: 'bottom top',
                scrub: true
            }
        });
    });
    
    // 背景图片视差
    gsap.utils.toArray('.parallax-bg').forEach(bg => {
        gsap.to(bg, {
            yPercent: -30,
            ease: 'none',
            scrollTrigger: {
                trigger: bg.parentElement,
                start: 'top bottom',
                end: 'bottom top',
                scrub: true
            }
        });
    });
}

// 悬停动画
function initHoverAnimations() {
    // 按钮悬停效果
    gsap.utils.toArray('.button').forEach(button => {
        button.addEventListener('mouseenter', () => {
            gsap.to(button, {
                scale: 1.05,
                duration: 0.3,
                ease: 'power2.out'
            });
        });
        
        button.addEventListener('mouseleave', () => {
            gsap.to(button, {
                scale: 1,
                duration: 0.3,
                ease: 'power2.out'
            });
        });
    });
    
    // 卡片悬停效果增强
    gsap.utils.toArray('.card').forEach(card => {
        const cardImage = card.querySelector('.card-image img');
        
        card.addEventListener('mouseenter', () => {
            gsap.to(card, {
                y: -10,
                duration: 0.3,
                ease: 'power2.out'
            });
            
            if (cardImage) {
                gsap.to(cardImage, {
                    scale: 1.1,
                    duration: 0.5,
                    ease: 'power2.out'
                });
            }
        });
        
        card.addEventListener('mouseleave', () => {
            gsap.to(card, {
                y: 0,
                duration: 0.3,
                ease: 'power2.out'
            });
            
            if (cardImage) {
                gsap.to(cardImage, {
                    scale: 1,
                    duration: 0.5,
                    ease: 'power2.out'
                });
            }
        });
    });
    
    // 导航项悬停效果
    gsap.utils.toArray('.navbar-item').forEach(item => {
        item.addEventListener('mouseenter', () => {
            gsap.to(item, {
                color: '#3273dc',
                duration: 0.3,
                ease: 'power2.out'
            });
        });
        
        item.addEventListener('mouseleave', () => {
            if (!item.classList.contains('is-active')) {
                gsap.to(item, {
                    color: '',
                    duration: 0.3,
                    ease: 'power2.out'
                });
            }
        });
    });
}

// 页面切换动画
function initPageTransitions() {
    // 为所有内部链接添加页面切换效果
    const internalLinks = document.querySelectorAll('a[href^="/"], a[href^="./"], a[href^="../"]');
    
    internalLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // 跳过锚点链接和外部链接
            if (href.startsWith('#') || href.startsWith('http')) {
                return;
            }
            
            e.preventDefault();
            
            // 页面退出动画
            const tl = gsap.timeline({
                onComplete: () => {
                    window.location.href = href;
                }
            });
            
            tl.to('.navbar', {
                y: -100,
                opacity: 0,
                duration: 0.5,
                ease: 'power2.in'
            });
            
            tl.to('main', {
                opacity: 0,
                y: 50,
                duration: 0.5,
                ease: 'power2.in'
            }, '-=0.3');
        });
    });
}

// 滚动进度指示器
function initScrollProgress() {
    const progressBar = document.querySelector('.scroll-progress');
    if (!progressBar) return;
    
    gsap.to(progressBar, {
        scaleX: 1,
        ease: 'none',
        scrollTrigger: {
            trigger: 'body',
            start: 'top top',
            end: 'bottom bottom',
            scrub: true
        }
    });
}

// 文字打字机效果
function initTypewriterEffect() {
    gsap.utils.toArray('.typewriter').forEach(element => {
        const text = element.textContent;
        element.textContent = '';
        
        gsap.to(element, {
            textContent: text,
            duration: text.length * 0.05,
            ease: 'none',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
}

// 图片加载动画
function initImageLoadAnimations() {
    const images = document.querySelectorAll('img[data-animate]');
    
    images.forEach(img => {
        img.addEventListener('load', () => {
            gsap.from(img, {
                opacity: 0,
                scale: 0.8,
                duration: 0.8,
                ease: 'power2.out'
            });
        });
    });
}

// 导出动画函数供其他模块使用
window.gsapAnimations = {
    fadeIn: (element, options = {}) => {
        return gsap.from(element, {
            opacity: 0,
            y: 50,
            duration: 0.8,
            ease: 'power2.out',
            ...options
        });
    },
    
    slideIn: (element, direction = 'left', options = {}) => {
        const x = direction === 'left' ? -100 : 100;
        return gsap.from(element, {
            opacity: 0,
            x: x,
            duration: 0.8,
            ease: 'power2.out',
            ...options
        });
    },
    
    scaleIn: (element, options = {}) => {
        return gsap.from(element, {
            opacity: 0,
            scale: 0.8,
            duration: 0.6,
            ease: 'back.out(1.7)',
            ...options
        });
    }
};

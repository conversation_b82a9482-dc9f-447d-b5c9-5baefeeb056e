@use "../utilities/css-variables" as cv;
@use "../utilities/initial-variables" as iv;
@use "../utilities/extends";
@use "../utilities/mixins";

$breadcrumb-item-color: cv.getVar("link-text") !default;
$breadcrumb-item-hover-color: cv.getVar("link-text-hover") !default;
$breadcrumb-item-active-color: cv.getVar("link-text-active") !default;

$breadcrumb-item-padding-vertical: 0 !default;
$breadcrumb-item-padding-horizontal: 0.75em !default;

$breadcrumb-item-separator-color: cv.getVar("border") !default;

.#{iv.$class-prefix}breadcrumb {
  @include cv.register-vars(
    (
      "breadcrumb-item-color": #{$breadcrumb-item-color},
      "breadcrumb-item-hover-color": #{$breadcrumb-item-hover-color},
      "breadcrumb-item-active-color": #{$breadcrumb-item-active-color},
      "breadcrumb-item-padding-vertical": #{$breadcrumb-item-padding-vertical},
      "breadcrumb-item-padding-horizontal": #{$breadcrumb-item-padding-horizontal},
      "breadcrumb-item-separator-color": #{$breadcrumb-item-separator-color},
    )
  );
}

.#{iv.$class-prefix}breadcrumb {
  @extend %block;
  @extend %unselectable;
  font-size: cv.getVar("size-normal");
  white-space: nowrap;

  a {
    align-items: center;
    color: cv.getVar("breadcrumb-item-color");
    display: flex;
    justify-content: center;
    padding: cv.getVar("breadcrumb-item-padding-vertical")
      cv.getVar("breadcrumb-item-padding-horizontal");

    &:hover {
      color: cv.getVar("breadcrumb-item-hover-color");
    }
  }

  li {
    align-items: center;
    display: flex;

    &:first-child a {
      padding-inline-start: 0;
    }

    &.#{iv.$class-prefix}is-active {
      a {
        color: cv.getVar("breadcrumb-item-active-color");
        cursor: default;
        pointer-events: none;
      }
    }

    & + li::before {
      color: cv.getVar("breadcrumb-item-separator-color");
      content: "/";
    }
  }

  ul,
  ol {
    align-items: flex-start;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
  }

  .#{iv.$class-prefix}icon {
    &:first-child {
      margin-inline-end: 0.5em;
    }

    &:last-child {
      margin-inline-start: 0.5em;
    }
  }

  // Alignment
  &.#{iv.$class-prefix}is-centered {
    ol,
    ul {
      justify-content: center;
    }
  }

  &.#{iv.$class-prefix}is-right {
    ol,
    ul {
      justify-content: flex-end;
    }
  }

  // Sizes
  &.#{iv.$class-prefix}is-small {
    font-size: cv.getVar("size-small");
  }

  &.#{iv.$class-prefix}is-medium {
    font-size: cv.getVar("size-medium");
  }

  &.#{iv.$class-prefix}is-large {
    font-size: cv.getVar("size-large");
  }

  // Styles
  &.#{iv.$class-prefix}has-arrow-separator {
    li + li::before {
      content: "→";
    }
  }

  &.#{iv.$class-prefix}has-bullet-separator {
    li + li::before {
      content: "•";
    }
  }

  &.#{iv.$class-prefix}has-dot-separator {
    li + li::before {
      content: "·";
    }
  }

  &.#{iv.$class-prefix}has-succeeds-separator {
    li + li::before {
      content: "≻";
    }
  }
}

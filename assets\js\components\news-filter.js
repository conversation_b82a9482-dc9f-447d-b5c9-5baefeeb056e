// 资讯筛选和搜索功能

class NewsFilter {
    constructor() {
        this.currentFilters = {
            category: 'all',
            sort: 'latest'
        };
        this.currentPage = 1;
        this.itemsPerPage = 6;
        this.allNews = [];
        this.filteredNews = [];
        this.searchTerm = '';
        
        this.init();
    }
    
    init() {
        this.bindFilterEvents();
        this.bindSearchEvents();
        this.bindPaginationEvents();
        this.bindLoadMoreEvent();
        this.loadNews();
        
        console.log('资讯筛选功能已初始化');
    }
    
    bindFilterEvents() {
        // 绑定筛选选项点击事件
        const filterOptions = document.querySelectorAll('.filter-option');
        filterOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleFilterClick(option);
            });
        });
    }
    
    bindSearchEvents() {
        const searchInput = document.getElementById('news-search');
        const searchBtn = document.getElementById('search-btn');
        
        if (searchInput) {
            // 实时搜索
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value.trim();
                this.debounceSearch();
            });
            
            // 回车搜索
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch();
                }
            });
        }
        
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.performSearch();
            });
        }
    }
    
    bindPaginationEvents() {
        // 绑定分页按钮事件
        const prevBtn = document.querySelector('.pagination-previous');
        const nextBtn = document.querySelector('.pagination-next');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (this.currentPage > 1) {
                    this.goToPage(this.currentPage - 1);
                }
            });
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', (e) => {
                e.preventDefault();
                const totalPages = Math.ceil(this.filteredNews.length / this.itemsPerPage);
                if (this.currentPage < totalPages) {
                    this.goToPage(this.currentPage + 1);
                }
            });
        }
    }
    
    bindLoadMoreEvent() {
        const loadMoreBtn = document.getElementById('load-more-news');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreNews();
            });
        }
    }
    
    handleFilterClick(option) {
        const filterGroup = option.closest('.filter-group');
        const filterType = this.getFilterType(filterGroup);
        const filterValue = option.getAttribute(`data-${filterType}`);
        
        // 更新活动状态
        filterGroup.querySelectorAll('.filter-option').forEach(opt => {
            opt.classList.remove('is-active');
        });
        option.classList.add('is-active');
        
        // 更新筛选条件
        this.currentFilters[filterType] = filterValue;
        this.currentPage = 1;
        
        // 应用筛选
        this.applyFilters();
    }
    
    getFilterType(filterGroup) {
        const label = filterGroup.querySelector('.filter-label').textContent;
        switch (label) {
            case '资讯分类': return 'category';
            case '排序方式': return 'sort';
            default: return 'category';
        }
    }
    
    loadNews() {
        // 获取页面上的资讯数据
        const newsItems = document.querySelectorAll('.news-item');
        this.allNews = Array.from(newsItems).map(item => {
            return {
                element: item,
                category: item.getAttribute('data-category'),
                date: item.getAttribute('data-date'),
                views: parseInt(item.getAttribute('data-views')),
                title: item.querySelector('.title a').textContent,
                content: item.querySelector('.content p').textContent,
                excerpt: item.querySelector('.content p').textContent
            };
        });
        
        this.filteredNews = [...this.allNews];
        this.applyFilters();
    }
    
    applyFilters() {
        // 筛选资讯
        this.filteredNews = this.allNews.filter(newsItem => {
            return this.matchesFilters(newsItem) && this.matchesSearch(newsItem);
        });
        
        // 排序
        this.sortNews();
        
        // 显示结果
        this.displayNews();
        
        // 更新分页
        this.updatePagination();
    }
    
    matchesFilters(newsItem) {
        // 检查分类筛选
        if (this.currentFilters.category !== 'all' && 
            newsItem.category !== this.currentFilters.category) {
            return false;
        }
        
        return true;
    }
    
    matchesSearch(newsItem) {
        if (!this.searchTerm) {
            return true;
        }
        
        const searchLower = this.searchTerm.toLowerCase();
        return newsItem.title.toLowerCase().includes(searchLower) ||
               newsItem.content.toLowerCase().includes(searchLower);
    }
    
    sortNews() {
        switch (this.currentFilters.sort) {
            case 'latest':
                this.filteredNews.sort((a, b) => b.date.localeCompare(a.date));
                break;
            case 'popular':
                // 模拟热门度排序（可以基于评论数、分享数等）
                this.filteredNews.sort(() => Math.random() - 0.5);
                break;
            case 'views':
                this.filteredNews.sort((a, b) => b.views - a.views);
                break;
        }
    }
    
    displayNews() {
        // 隐藏所有资讯
        this.allNews.forEach(newsItem => {
            newsItem.element.style.display = 'none';
        });
        
        // 计算当前页显示的资讯
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const newsToShow = this.filteredNews.slice(startIndex, endIndex);
        
        // 显示当前页的资讯
        newsToShow.forEach((newsItem, index) => {
            newsItem.element.style.display = 'block';
            
            // 添加动画效果
            setTimeout(() => {
                newsItem.element.classList.add('fade-in', 'is-visible');
            }, index * 100);
        });
        
        // 显示结果统计
        this.showResultsCount();
    }
    
    showResultsCount() {
        const totalResults = this.filteredNews.length;
        const startIndex = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endIndex = Math.min(this.currentPage * this.itemsPerPage, totalResults);
        
        // 创建或更新结果统计显示
        let resultsInfo = document.querySelector('.news-results-info');
        if (!resultsInfo) {
            resultsInfo = document.createElement('div');
            resultsInfo.className = 'news-results-info has-text-centered mb-4';
            const grid = document.getElementById('news-grid');
            grid.parentNode.insertBefore(resultsInfo, grid);
        }
        
        if (totalResults === 0) {
            resultsInfo.innerHTML = '<p class="has-text-grey">未找到符合条件的资讯</p>';
        } else {
            let resultText = `显示第 ${startIndex}-${endIndex} 项，共 ${totalResults} 篇资讯`;
            if (this.searchTerm) {
                resultText += ` (搜索: "${this.searchTerm}")`;
            }
            resultsInfo.innerHTML = `<p class="has-text-grey">${resultText}</p>`;
        }
    }
    
    updatePagination() {
        const totalPages = Math.ceil(this.filteredNews.length / this.itemsPerPage);
        const pagination = document.querySelector('.pagination');
        
        if (totalPages <= 1) {
            pagination.style.display = 'none';
            return;
        }
        
        pagination.style.display = 'flex';
        
        // 更新上一页按钮
        const prevBtn = pagination.querySelector('.pagination-previous');
        if (this.currentPage === 1) {
            prevBtn.setAttribute('disabled', '');
        } else {
            prevBtn.removeAttribute('disabled');
        }
        
        // 更新下一页按钮
        const nextBtn = pagination.querySelector('.pagination-next');
        if (this.currentPage === totalPages) {
            nextBtn.setAttribute('disabled', '');
        } else {
            nextBtn.removeAttribute('disabled');
        }
        
        // 更新页码列表
        this.updatePageNumbers(totalPages);
    }
    
    updatePageNumbers(totalPages) {
        const pageList = document.querySelector('.pagination-list');
        pageList.innerHTML = '';
        
        // 计算显示的页码范围
        const maxVisible = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(totalPages, startPage + maxVisible - 1);
        
        if (endPage - startPage < maxVisible - 1) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }
        
        // 添加页码
        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement('li');
            const a = document.createElement('a');
            a.className = 'pagination-link';
            a.textContent = i;
            a.setAttribute('aria-label', `转到第${i}页`);
            
            if (i === this.currentPage) {
                a.classList.add('is-current');
                a.setAttribute('aria-current', 'page');
            }
            
            a.addEventListener('click', (e) => {
                e.preventDefault();
                this.goToPage(i);
            });
            
            li.appendChild(a);
            pageList.appendChild(li);
        }
        
        // 添加省略号和最后一页
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsis = document.createElement('li');
                ellipsis.innerHTML = '<span class="pagination-ellipsis">&hellip;</span>';
                pageList.appendChild(ellipsis);
            }
            
            const lastPage = document.createElement('li');
            const lastLink = document.createElement('a');
            lastLink.className = 'pagination-link';
            lastLink.textContent = totalPages;
            lastLink.setAttribute('aria-label', `转到第${totalPages}页`);
            lastLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.goToPage(totalPages);
            });
            lastPage.appendChild(lastLink);
            pageList.appendChild(lastPage);
        }
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.displayNews();
        this.updatePagination();
        
        // 滚动到顶部
        document.querySelector('#news-grid').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
    
    debounceSearch() {
        // 防抖搜索
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.performSearch();
        }, 300);
    }
    
    performSearch() {
        this.currentPage = 1;
        this.applyFilters();
        
        // 高亮搜索结果
        if (this.searchTerm) {
            this.highlightSearchResults();
        }
    }
    
    highlightSearchResults() {
        // 高亮显示搜索关键词
        const visibleNews = this.filteredNews.slice(
            (this.currentPage - 1) * this.itemsPerPage,
            this.currentPage * this.itemsPerPage
        );
        
        visibleNews.forEach(newsItem => {
            const titleElement = newsItem.element.querySelector('.title a');
            const contentElement = newsItem.element.querySelector('.content p');
            
            if (titleElement && contentElement) {
                titleElement.innerHTML = this.highlightText(titleElement.textContent, this.searchTerm);
                contentElement.innerHTML = this.highlightText(contentElement.textContent, this.searchTerm);
            }
        });
    }
    
    highlightText(text, searchTerm) {
        if (!searchTerm) return text;
        
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }
    
    loadMoreNews() {
        const loadMoreBtn = document.getElementById('load-more-news');
        const originalText = loadMoreBtn.innerHTML;
        
        loadMoreBtn.innerHTML = '<span class="loading-spinner"></span> 加载中...';
        loadMoreBtn.disabled = true;
        
        // 模拟加载更多资讯
        setTimeout(() => {
            loadMoreBtn.innerHTML = originalText;
            loadMoreBtn.disabled = false;
            
            showNotification('暂无更多资讯', 'info');
        }, 1000);
    }
    
    // 获取URL参数中的筛选条件
    getUrlFilters() {
        const urlParams = new URLSearchParams(window.location.search);
        const category = urlParams.get('category');
        const search = urlParams.get('search');
        
        if (category) {
            this.currentFilters.category = category;
            this.updateFilterButtonState('category', category);
        }
        
        if (search) {
            this.searchTerm = search;
            const searchInput = document.getElementById('news-search');
            if (searchInput) {
                searchInput.value = search;
            }
        }
    }
    
    updateFilterButtonState(filterType, value) {
        const filterOptions = document.querySelectorAll(`[data-${filterType}]`);
        filterOptions.forEach(option => {
            option.classList.remove('is-active');
            if (option.getAttribute(`data-${filterType}`) === value) {
                option.classList.add('is-active');
            }
        });
    }
    
    // 添加资讯收藏功能
    toggleBookmark(newsId) {
        const bookmarks = JSON.parse(localStorage.getItem('news-bookmarks') || '[]');
        const index = bookmarks.indexOf(newsId);
        
        if (index > -1) {
            bookmarks.splice(index, 1);
            showNotification('已取消收藏', 'info');
        } else {
            bookmarks.push(newsId);
            showNotification('已添加到收藏', 'success');
        }
        
        localStorage.setItem('news-bookmarks', JSON.stringify(bookmarks));
    }
    
    // 添加阅读历史记录
    addToReadingHistory(newsId, title) {
        const history = JSON.parse(localStorage.getItem('reading-history') || '[]');
        const existingIndex = history.findIndex(item => item.id === newsId);
        
        const historyItem = {
            id: newsId,
            title: title,
            timestamp: new Date().toISOString()
        };
        
        if (existingIndex > -1) {
            history[existingIndex] = historyItem;
        } else {
            history.unshift(historyItem);
            // 限制历史记录数量
            if (history.length > 50) {
                history.pop();
            }
        }
        
        localStorage.setItem('reading-history', JSON.stringify(history));
    }
}

// 初始化资讯筛选功能
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('news-grid')) {
        window.newsFilter = new NewsFilter();
    }
});

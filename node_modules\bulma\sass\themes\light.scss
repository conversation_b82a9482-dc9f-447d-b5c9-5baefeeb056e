@use "sass:list";
@use "sass:meta";

@use "../utilities/css-variables" as cv;
@use "../utilities/derived-variables" as dv;
@use "../utilities/initial-variables" as iv;
@use "../utilities/functions" as fn;
@use "setup";

// The main lightness of this theme
$scheme-main-l: 100%;

// The main scheme color, used to make calculations
$scheme-main: hsl(iv.$scheme-h, iv.$scheme-s, $scheme-main-l);

@mixin light-theme {
  @include cv.register-vars(
    (
      "scheme-h": iv.$scheme-h,
      "scheme-s": iv.$scheme-s,
      "light-l": iv.$light-l,
      "light-invert-l": iv.$dark-l,
      "dark-l": iv.$dark-l,
      "dark-invert-l": iv.$light-l,
      "soft-l": iv.$light-l,
      "bold-l": iv.$dark-l,
      "soft-invert-l": iv.$dark-l,
      "bold-invert-l": iv.$light-l,

      // Deltas
      "hover-background-l-delta": -5%,
      "active-background-l-delta": -10%,

      "hover-border-l-delta": -10%,
      "active-border-l-delta": -20%,

      "hover-color-l-delta": -5%,
      "active-color-l-delta": -10%,

      "hover-shadow-a-delta": -0.05,
      "active-shadow-a-delta": -0.1,

      // Light only
      "scheme-brightness": "light",
      "scheme-main-l": $scheme-main-l,
      "scheme-main-bis-l": 98%,
      "scheme-main-ter-l": 96%,
      "background-l": 96%,
      "border-weak-l": 93%,
      "border-l": 86%,
      "text-weak-l": 48%,
      "text-l": 29%,
      "text-strong-l": 21%,
      "text-title-l": 14%,
      "scheme-invert-ter-l": 14%,
      "scheme-invert-bis-l": 7%,
      "scheme-invert-l": 4%,

      // Typography
      "family-primary": dv.$family-primary,
      "family-secondary": dv.$family-secondary,
      "family-code": dv.$family-code,
      "size-small": dv.$size-small,
      "size-normal": dv.$size-normal,
      "size-medium": dv.$size-medium,
      "size-large": dv.$size-large,
      "weight-light": iv.$weight-light,
      "weight-normal": iv.$weight-normal,
      "weight-medium": iv.$weight-medium,
      "weight-semibold": iv.$weight-semibold,
      "weight-bold": iv.$weight-bold,
      "weight-extrabold": iv.$weight-extrabold,

      // Other
      "block-spacing": iv.$block-spacing,
      "duration": iv.$duration,
      "easing": iv.$easing,
      "radius-small": iv.$radius-small,
      "radius": iv.$radius,
      "radius-medium": iv.$radius-medium,
      "radius-large": iv.$radius-large,
      "radius-rounded": iv.$radius-rounded,
      "speed": iv.$speed,

      "arrow-color": #{cv.getVar("link")},
      "loading-color": #{cv.getVar("border")},
      "burger-h": #{cv.getVar("link-h")},
      "burger-s": #{cv.getVar("link-s")},
      "burger-l": #{cv.getVar("link-l")},
      "burger-border-radius": 0.5em,
      // Using px values to prevent half pixel issues
      "burger-gap": 5px,
      "burger-item-height": 2px,
      "burger-item-width": 20px,
    )
  );

  // Colors
  $no-palette: ("white", "black", "light", "dark");

  @each $name, $color in dv.$colors {
    $base: $color;
    $invert: null;
    $light: null;
    $dark: null;

    @if meta.type-of($color == "list") {
      $base: list.nth($color, 1);

      @if list.length($color) > 3 {
        $invert: list.nth($color, 2);
        $light: list.nth($color, 3);
        $dark: list.nth($color, 4);
      } @else if list.length($color) > 1 {
        $invert: list.nth($color, 2);
      }
    }

    @if list.index($no-palette, $name) {
      @include cv.generate-basic-palette($name, $base, $invert);
    } @else {
      @include cv.generate-color-palette(
        $name,
        $base,
        $scheme-main-l,
        $invert,
        $light,
        $dark
      );
    }

    @include cv.generate-on-scheme-colors($name, $base, $scheme-main);
  }

  // Shades
  @each $name, $shade in dv.$shades {
    @include cv.register-var($name, $shade);
  }

  @include cv.register-hsl("shadow", dv.$shadow-color);

  @each $size in dv.$sizes {
    $i: list.index(dv.$sizes, $size);
    $name: "size-#{$i}";
    @include cv.register-var($name, $size);
  }
}

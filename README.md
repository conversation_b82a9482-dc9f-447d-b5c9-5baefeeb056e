# 建筑科技公司官网

专业的建筑科技公司官网，专注于玻璃幕墙和铝合金门窗业务的现代化企业展示平台。

## 项目特点

- 🎨 **现代设计**: 基于 Bulma CSS 框架的现代化界面设计
- 📱 **响应式布局**: 完美适配桌面、平板和移动设备
- 🌙 **主题切换**: 支持明暗主题切换，提升用户体验
- ⚡ **高性能动画**: 使用 GSAP 实现流畅的动画效果
- 🔍 **SEO 优化**: 语义化 HTML 结构，利于搜索引擎优化
- ♿ **无障碍访问**: 遵循 WCAG 无障碍访问标准

## 技术栈

- **Bulma Sass v1.0.4** - CSS 框架
- **Font Awesome** - 图标库
- **GSAP v3.13.0** - 动画库
- **HTML5** - 语义化标记
- **JavaScript (ES6+)** - 交互逻辑
- **Sass** - CSS 预处理器

## 项目结构

```
project-root/
├── index.html                 # 网站首页
├── template.html              # HTML 模板文件
├── assets/                    # 静态资源目录
│   ├── css/                   # 编译后的 CSS 文件
│   │   ├── main.css          # 主样式文件
│   │   └── main.css.map      # 源映射文件
│   ├── js/                    # JavaScript 文件
│   │   ├── main.js           # 主脚本文件
│   │   ├── theme-switch.js   # 主题切换功能
│   │   ├── animation.js      # GSAP 动画效果
│   │   └── components/       # 组件脚本目录
│   ├── sass/                  # Sass 源文件
│   │   ├── main.scss         # 主 Sass 文件
│   │   ├── _variables.scss   # 变量定义
│   │   ├── _components.scss  # 组件样式
│   │   └── themes/           # 主题样式
│   │       ├── _light.scss   # 明主题
│   │       └── _dark.scss    # 暗主题
│   ├── images/                # 图片资源
│   │   ├── logo/             # Logo 图片
│   │   ├── products/         # 产品图片
│   │   ├── cases/            # 案例图片
│   │   ├── banners/          # 横幅图片
│   │   └── icons/            # 图标文件
│   └── fonts/                 # 字体文件
├── package.json               # 项目配置文件
└── README.md                  # 项目说明文档
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 编译样式

```bash
# 编译 CSS（压缩版）
npm run build-css

# 监听模式（开发时使用）
npm run watch-css

# 或者使用开发模式
npm run dev
```

### 3. 启动本地服务器

由于项目使用静态 HTML 文件，您可以：

- 使用 VS Code 的 Live Server 扩展
- 使用 Python 简单服务器：`python -m http.server 8000`
- 使用 Node.js 的 http-server：`npx http-server`

### 4. 访问网站

打开浏览器访问 `http://localhost:8000`（端口号根据您使用的服务器而定）

## 功能特性

### 主题切换
- 支持明暗主题切换
- 自动保存用户偏好设置
- 跟随系统主题设置

### 响应式设计
- 移动端优先的设计理念
- 流畅的断点过渡
- 优化的触摸交互

### 动画效果
- 页面加载动画
- 滚动触发动画
- 悬停交互效果
- 平滑的页面过渡

### 性能优化
- 图片懒加载
- CSS 和 JS 压缩
- 关键资源预加载
- 优化的渲染路径

## 浏览器支持

- Chrome (最新版本)
- Firefox (最新版本)
- Safari (最新版本)
- Edge (最新版本)

## 开发指南

### 添加新页面

1. 复制 `template.html` 文件
2. 修改页面标题和内容
3. 更新导航栏的活动状态
4. 添加页面特定的样式和脚本

### 自定义样式

1. 在 `assets/sass/_variables.scss` 中修改变量
2. 在 `assets/sass/_components.scss` 中添加组件样式
3. 运行 `npm run build-css` 编译样式

### 添加动画效果

1. 在 HTML 中添加动画类名（如 `fade-in`, `slide-in-left`）
2. 在 `assets/js/animation.js` 中自定义动画效果
3. 使用 GSAP 的 ScrollTrigger 插件实现滚动触发动画

## 部署

### 静态网站托管

推荐使用以下平台部署：

- **Netlify**: 拖拽部署，自动 HTTPS
- **Vercel**: GitHub 集成，边缘网络
- **GitHub Pages**: 免费托管，版本控制

### 部署步骤

1. 编译生产版本的 CSS：`npm run build-css`
2. 上传所有文件到托管平台
3. 配置自定义域名（可选）
4. 设置 404 页面重定向

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系：
- 邮箱：<EMAIL>
- 电话：400-123-4567

---

© 2024 建筑科技公司. 保留所有权利.

@use "../utilities/css-variables" as cv;
@use "../utilities/derived-variables" as dv;
@use "../utilities/initial-variables" as iv;
@use "../utilities/extends";
@use "../utilities/mixins" as mx;

$content-heading-color: #{cv.getVar("text-strong")} !default;
$content-heading-weight: #{cv.getVar("weight-extrabold")} !default;
$content-heading-line-height: 1.125 !default;

$content-block-margin-bottom: 1em !default;

$content-blockquote-background-color: #{cv.getVar("background")} !default;
$content-blockquote-border-left: 5px solid #{cv.getVar("border")} !default;
$content-blockquote-padding: 1.25em 1.5em !default;

$content-pre-padding: 1.25em 1.5em !default;

$content-table-cell-border: 1px solid #{cv.getVar("border")} !default;
$content-table-cell-border-width: 0 0 1px !default;
$content-table-cell-padding: 0.5em 0.75em !default;
$content-table-cell-heading-color: #{cv.getVar("text-strong")} !default;
$content-table-head-cell-border-width: 0 0 2px !default;
$content-table-head-cell-color: #{cv.getVar("text-strong")} !default;
$content-table-body-last-row-cell-border-bottom-width: 0 !default;
$content-table-foot-cell-border-width: 2px 0 0 !default;
$content-table-foot-cell-color: #{cv.getVar("text-strong")} !default;

.#{iv.$class-prefix}content {
  @include cv.register-vars(
    (
      "content-heading-color": #{$content-heading-color},
      "content-heading-weight": #{$content-heading-weight},
      "content-heading-line-height": #{$content-heading-line-height},
      "content-block-margin-bottom": #{$content-block-margin-bottom},
      "content-blockquote-background-color": #{$content-blockquote-background-color},
      "content-blockquote-border-left": #{$content-blockquote-border-left},
      "content-blockquote-padding": #{$content-blockquote-padding},
      "content-pre-padding": #{$content-pre-padding},
      "content-table-cell-border": #{$content-table-cell-border},
      "content-table-cell-border-width": #{$content-table-cell-border-width},
      "content-table-cell-padding": #{$content-table-cell-padding},
      "content-table-cell-heading-color": #{$content-table-cell-heading-color},
      "content-table-head-cell-border-width": #{$content-table-head-cell-border-width},
      "content-table-head-cell-color": #{$content-table-head-cell-color},
      "content-table-body-last-row-cell-border-bottom-width": #{$content-table-body-last-row-cell-border-bottom-width},
      "content-table-foot-cell-border-width": #{$content-table-foot-cell-border-width},
      "content-table-foot-cell-color": #{$content-table-foot-cell-color},
    )
  );
}

.#{iv.$class-prefix}content {
  @extend %block;

  // Inline
  li + li {
    margin-top: 0.25em;
  }

  // Block
  p,
  dl,
  ol,
  ul,
  blockquote,
  pre,
  table {
    &:not(:last-child) {
      margin-bottom: cv.getVar("content-block-margin-bottom");
    }
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: cv.getVar("content-heading-color");
    font-weight: cv.getVar("content-heading-weight");
    line-height: cv.getVar("content-heading-line-height");
  }

  h1 {
    font-size: 2em;
    margin-bottom: 0.5em;

    &:not(:first-child) {
      margin-top: 1em;
    }
  }

  h2 {
    font-size: 1.75em;
    margin-bottom: 0.5714em;

    &:not(:first-child) {
      margin-top: 1.1428em;
    }
  }

  h3 {
    font-size: 1.5em;
    margin-bottom: 0.6666em;

    &:not(:first-child) {
      margin-top: 1.3333em;
    }
  }

  h4 {
    font-size: 1.25em;
    margin-bottom: 0.8em;
  }

  h5 {
    font-size: 1.125em;
    margin-bottom: 0.8888em;
  }

  h6 {
    font-size: 1em;
    margin-bottom: 1em;
  }

  blockquote {
    background-color: cv.getVar("content-blockquote-background-color");
    border-inline-start: cv.getVar("content-blockquote-border-left");
    padding: cv.getVar("content-blockquote-padding");
  }

  ol {
    list-style-position: outside;
    margin-inline-start: 2em;

    &:not(:first-child) {
      margin-top: 1em;
    }

    &:not([type]) {
      list-style-type: decimal;

      &.#{iv.$class-prefix}is-lower-alpha {
        list-style-type: lower-alpha;
      }

      &.#{iv.$class-prefix}is-lower-roman {
        list-style-type: lower-roman;
      }

      &.#{iv.$class-prefix}is-upper-alpha {
        list-style-type: upper-alpha;
      }

      &.#{iv.$class-prefix}is-upper-roman {
        list-style-type: upper-roman;
      }
    }
  }

  ul {
    list-style: disc outside;
    margin-inline-start: 2em;

    &:not(:first-child) {
      margin-top: 1em;
    }

    ul {
      list-style-type: circle;
      margin-bottom: 0.25em;
      margin-top: 0.25em;

      ul {
        list-style-type: square;
      }
    }
  }

  dd {
    margin-inline-start: 2em;
  }

  figure:not([class]) {
    margin-left: 2em;
    margin-right: 2em;
    text-align: center;

    &:not(:first-child) {
      margin-top: 2em;
    }

    &:not(:last-child) {
      margin-bottom: 2em;
    }

    img {
      display: inline-block;
    }

    figcaption {
      font-style: italic;
    }
  }

  pre {
    @include mx.overflow-touch;
    overflow-x: auto;
    padding: cv.getVar("content-pre-padding");
    white-space: pre;
    word-wrap: normal;
  }

  sup,
  sub {
    font-size: 75%;
  }

  table {
    td,
    th {
      border: cv.getVar("content-table-cell-border");
      border-width: cv.getVar("content-table-cell-border-width");
      padding: cv.getVar("content-table-cell-padding");
      vertical-align: top;
    }

    th {
      color: cv.getVar("content-table-cell-heading-color");

      &:not([align]) {
        text-align: inherit;
      }
    }

    thead {
      td,
      th {
        border-width: cv.getVar("content-table-head-cell-border-width");
        color: cv.getVar("content-table-head-cell-color");
      }
    }

    tfoot {
      td,
      th {
        border-width: cv.getVar("content-table-foot-cell-border-width");
        color: cv.getVar("content-table-foot-cell-color");
      }
    }

    tbody {
      tr {
        &:last-child {
          td,
          th {
            border-bottom-width: cv.getVar(
              "content-table-body-last-row-cell-border-bottom-width"
            );
          }
        }
      }
    }
  }

  .#{iv.$class-prefix}tabs {
    li + li {
      margin-top: 0;
    }
  }

  // Sizes
  &.#{iv.$class-prefix}is-small {
    font-size: cv.getVar("size-small");
  }

  &.#{iv.$class-prefix}is-normal {
    font-size: cv.getVar("size-normal");
  }

  &.#{iv.$class-prefix}is-medium {
    font-size: cv.getVar("size-medium");
  }

  &.#{iv.$class-prefix}is-large {
    font-size: cv.getVar("size-large");
  }
}

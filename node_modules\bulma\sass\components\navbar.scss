@use "../utilities/css-variables" as cv;
@use "../utilities/initial-variables" as iv;
@use "../utilities/derived-variables" as dv;
@use "../utilities/extends";
@use "../utilities/mixins" as mx;

$navbar-h: cv.getVar("scheme-h") !default;
$navbar-s: cv.getVar("scheme-s") !default;
$navbar-l: cv.getVar("scheme-main-l") !default;
$navbar-background-color: cv.getVar("scheme-main") !default;
$navbar-box-shadow-size: 0 0.125em 0 0 !default;
$navbar-box-shadow-color: cv.getVar("background") !default;
$navbar-height: 3.25rem !default;
$navbar-padding-vertical: 1rem !default;
$navbar-padding-horizontal: 2rem !default;
$navbar-z: 30 !default;
$navbar-fixed-z: 30 !default;

$navbar-item-background-a: 0 !default;
$navbar-item-background-l: cv.getVar("scheme-main-l") !default;
$navbar-item-background-l-delta: 0% !default;
$navbar-item-hover-background-l-delta: cv.getVar(
  "hover-background-l-delta"
) !default;
$navbar-item-active-background-l-delta: cv.getVar(
  "active-background-l-delta"
) !default;
$navbar-item-color-l: cv.getVar("text-l") !default;
$navbar-item-color: hsl(
  #{cv.getVar("navbar-h")},
  #{cv.getVar("navbar-s")},
  #{cv.getVar("navbar-item-color-l")}
) !default;
$navbar-item-selected-h: cv.getVar("link-h") !default;
$navbar-item-selected-s: cv.getVar("link-s") !default;
$navbar-item-selected-l: cv.getVar("link-l") !default;
$navbar-item-selected-background-l: cv.getVar("link-l") !default;
$navbar-item-selected-color-l: cv.getVar("link-invert-l") !default;
$navbar-item-img-max-height: 1.75rem !default;

$navbar-dropdown-item-h: cv.getVar("scheme-h") !default;
$navbar-dropdown-item-s: cv.getVar("scheme-s") !default;
$navbar-dropdown-item-l: cv.getVar("scheme-main-l") !default;
$navbar-dropdown-item-background-l: cv.getVar("scheme-main-l") !default;
$navbar-dropdown-item-color-l: cv.getVar("text-l") !default;

$navbar-burger-color: cv.getVar("link") !default;

$navbar-tab-hover-background-color: transparent !default;
$navbar-tab-hover-border-bottom-color: cv.getVar("link") !default;
$navbar-tab-active-color: cv.getVar("link") !default;
$navbar-tab-active-background-color: transparent !default;
$navbar-tab-active-border-bottom-color: cv.getVar("link") !default;
$navbar-tab-active-border-bottom-style: solid !default;
$navbar-tab-active-border-bottom-width: 0.1875em !default;

$navbar-dropdown-background-color: cv.getVar("scheme-main") !default;
$navbar-dropdown-border-l: cv.getVar("border-l") !default;
$navbar-dropdown-border-color: hsl(
  cv.getVar("navbar-h"),
  cv.getVar("navbar-s"),
  cv.getVar("navbar-dropdown-border-l")
) !default;
$navbar-dropdown-border-style: solid !default;
$navbar-dropdown-border-width: 0.125em !default;
$navbar-dropdown-offset: -0.25em !default;
$navbar-dropdown-arrow: cv.getVar("link") !default;
$navbar-dropdown-radius: cv.getVar("radius-large") !default;
$navbar-dropdown-z: 20 !default;

$navbar-dropdown-boxed-radius: cv.getVar("radius-large") !default;
$navbar-dropdown-boxed-shadow:
  0 0.5em 0.5em
    hsla(
      #{cv.getVar("scheme-h")},
      #{cv.getVar("scheme-s")},
      #{cv.getVar("scheme-invert-l")},
      0.1
    ),
  0 0 0 1px
    hsla(
      #{cv.getVar("scheme-h")},
      #{cv.getVar("scheme-s")},
      #{cv.getVar("scheme-invert-l")},
      0.1
    ) !default;

$navbar-divider-background-l: cv.getVar("background-l") !default;
$navbar-divider-height: 0.125em !default;

$navbar-bottom-box-shadow-size: 0 -0.125em 0 0 !default;

$navbar-breakpoint: iv.$desktop !default;

$navbar-colors: dv.$colors !default;

@mixin navbar-fixed {
  left: 0;
  position: fixed;
  right: 0;
  z-index: cv.getVar("navbar-fixed-z");
}

#{iv.$variables-host} {
  @include cv.register-vars(
    (
      "navbar-height": #{$navbar-height},
    )
  );
}

.#{iv.$class-prefix}navbar {
  @include cv.register-vars(
    (
      "navbar-h": #{$navbar-h},
      "navbar-s": #{$navbar-s},
      "navbar-l": #{$navbar-l},
      "navbar-background-color": #{$navbar-background-color},
      "navbar-box-shadow-size": #{$navbar-box-shadow-size},
      "navbar-box-shadow-color": #{$navbar-box-shadow-color},
      "navbar-padding-vertical": #{$navbar-padding-vertical},
      "navbar-padding-horizontal": #{$navbar-padding-horizontal},
      "navbar-z": #{$navbar-z},
      "navbar-fixed-z": #{$navbar-fixed-z},
      "navbar-item-background-a": #{$navbar-item-background-a},
      "navbar-item-background-l": #{$navbar-item-background-l},
      "navbar-item-background-l-delta": #{$navbar-item-background-l-delta},
      "navbar-item-hover-background-l-delta": #{$navbar-item-hover-background-l-delta},
      "navbar-item-active-background-l-delta": #{$navbar-item-active-background-l-delta},
      "navbar-item-color-l": #{$navbar-item-color-l},
      "navbar-item-color": #{$navbar-item-color},
      "navbar-item-selected-h": #{$navbar-item-selected-h},
      "navbar-item-selected-s": #{$navbar-item-selected-s},
      "navbar-item-selected-l": #{$navbar-item-selected-l},
      "navbar-item-selected-background-l": #{$navbar-item-selected-background-l},
      "navbar-item-selected-color-l": #{$navbar-item-selected-color-l},
      "navbar-item-img-max-height": #{$navbar-item-img-max-height},
      "navbar-burger-color": #{$navbar-burger-color},
      "navbar-tab-hover-background-color": #{$navbar-tab-hover-background-color},
      "navbar-tab-hover-border-bottom-color": #{$navbar-tab-hover-border-bottom-color},
      "navbar-tab-active-color": #{$navbar-tab-active-color},
      "navbar-tab-active-background-color": #{$navbar-tab-active-background-color},
      "navbar-tab-active-border-bottom-color": #{$navbar-tab-active-border-bottom-color},
      "navbar-tab-active-border-bottom-style": #{$navbar-tab-active-border-bottom-style},
      "navbar-tab-active-border-bottom-width": #{$navbar-tab-active-border-bottom-width},
      "navbar-dropdown-background-color": #{$navbar-dropdown-background-color},
      "navbar-dropdown-border-l": #{$navbar-dropdown-border-l},
      "navbar-dropdown-border-color": #{$navbar-dropdown-border-color},
      "navbar-dropdown-border-style": #{$navbar-dropdown-border-style},
      "navbar-dropdown-border-width": #{$navbar-dropdown-border-width},
      "navbar-dropdown-offset": #{$navbar-dropdown-offset},
      "navbar-dropdown-arrow": #{$navbar-dropdown-arrow},
      "navbar-dropdown-radius": #{$navbar-dropdown-radius},
      "navbar-dropdown-z": #{$navbar-dropdown-z},
      "navbar-dropdown-boxed-radius": #{$navbar-dropdown-boxed-radius},
      "navbar-dropdown-boxed-shadow": #{$navbar-dropdown-boxed-shadow},
      "navbar-dropdown-item-h": #{$navbar-dropdown-item-h},
      "navbar-dropdown-item-s": #{$navbar-dropdown-item-s},
      "navbar-dropdown-item-l": #{$navbar-dropdown-item-l},
      "navbar-dropdown-item-background-l": #{$navbar-dropdown-item-background-l},
      "navbar-dropdown-item-color-l": #{$navbar-dropdown-item-color-l},
      "navbar-divider-background-l": #{$navbar-divider-background-l},
      "navbar-divider-height": #{$navbar-divider-height},
      "navbar-bottom-box-shadow-size": #{$navbar-bottom-box-shadow-size},
    )
  );
}

.#{iv.$class-prefix}navbar {
  background-color: cv.getVar("navbar-background-color");
  min-height: cv.getVar("navbar-height");
  position: relative;
  z-index: cv.getVar("navbar-z");

  @each $name, $pair in $navbar-colors {
    &.#{iv.$class-prefix}is-#{$name} {
      @include cv.register-vars(
        (
          "navbar-h": #{cv.getVar($name, "", "-h")},
          "navbar-s": #{cv.getVar($name, "", "-s")},
          "navbar-l": #{cv.getVar($name, "", "-l")},
          "burger-h": #{cv.getVar($name, "", "-h")},
          "burger-s": #{cv.getVar($name, "", "-s")},
          "burger-l": #{cv.getVar($name, "", "-invert-l")},
          "navbar-background-color": #{cv.getVar($name)},
          "navbar-item-background-l": #{cv.getVar($name, "", "-l")},
          "navbar-item-color-l": #{cv.getVar($name, "", "-invert-l")},
          "navbar-item-selected-h": #{cv.getVar($name, "", "-h")},
          "navbar-item-selected-s": #{cv.getVar($name, "", "-s")},
          "navbar-item-selected-l": #{cv.getVar($name, "", "-l")},
          "navbar-item-selected-background-l": #{cv.getVar($name, "", "-l")},
          "navbar-item-selected-color-l": #{cv.getVar($name, "", "-invert-l")},
          "navbar-dropdown-arrow": #{cv.getVar($name, "", "-invert-l")},
          "navbar-dropdown-background-color":
            hsl(
              #{cv.getVar($name, "", "-h")},
              #{cv.getVar($name, "", "-s")},
              #{cv.getVar("navbar-dropdown-item-background-l")}
            ),
          "navbar-dropdown-item-h": #{cv.getVar($name, "", "-h")},
          "navbar-dropdown-item-s": #{cv.getVar($name, "", "-s")},
        )
      );
    }
  }

  & > .#{iv.$class-prefix}container {
    align-items: stretch;
    display: flex;
    min-height: cv.getVar("navbar-height");
    width: 100%;
  }

  &.#{iv.$class-prefix}has-shadow {
    box-shadow: cv.getVar("navbar-box-shadow-size")
      cv.getVar("navbar-box-shadow-color");
  }

  &.#{iv.$class-prefix}is-fixed-bottom,
  &.#{iv.$class-prefix}is-fixed-top {
    @include navbar-fixed;
  }

  &.#{iv.$class-prefix}is-fixed-bottom {
    bottom: 0;

    &.#{iv.$class-prefix}has-shadow {
      box-shadow: cv.getVar("navbar-bottom-box-shadow-size")
        cv.getVar("navbar-box-shadow-color");
    }
  }

  &.#{iv.$class-prefix}is-fixed-top {
    top: 0;
  }
}

html,
body {
  &.#{iv.$class-prefix}has-navbar-fixed-top {
    padding-top: cv.getVar("navbar-height");
  }

  &.#{iv.$class-prefix}has-navbar-fixed-bottom {
    padding-bottom: cv.getVar("navbar-height");
  }
}

.#{iv.$class-prefix}navbar-brand,
.#{iv.$class-prefix}navbar-tabs {
  align-items: stretch;
  display: flex;
  flex-shrink: 0;
  min-height: cv.getVar("navbar-height");
}

.#{iv.$class-prefix}navbar-tabs {
  @include mx.overflow-touch;
  max-width: 100vw;
  overflow-x: auto;
  overflow-y: hidden;
}

.#{iv.$class-prefix}navbar-burger {
  @extend %reset;
  @include mx.burger(2.5rem);

  & {
    align-self: center;
    color: cv.getVar("navbar-burger-color");
    margin-inline-start: auto;
    margin-inline-end: 0.375rem;
  }
}

.#{iv.$class-prefix}navbar-menu {
  display: none;
}

.#{iv.$class-prefix}navbar-item,
.#{iv.$class-prefix}navbar-link {
  color: cv.getVar("navbar-item-color");
  display: block;
  gap: 0.75rem;
  line-height: 1.5;
  padding: 0.5rem 0.75rem;
  position: relative;

  .#{iv.$class-prefix}icon {
    &:only-child {
      margin-left: -0.25rem;
      margin-right: -0.25rem;
    }
  }
}

a.#{iv.$class-prefix}navbar-item,
.#{iv.$class-prefix}navbar-link {
  background-color: hsla(
    #{cv.getVar("navbar-h")},
    #{cv.getVar("navbar-s")},
    calc(
      #{cv.getVar("navbar-item-background-l")} + #{cv.getVar(
          "navbar-item-background-l-delta"
        )}
    ),
    #{cv.getVar("navbar-item-background-a")}
  );
  cursor: pointer;

  &:focus,
  &:focus-within,
  &:hover {
    @include cv.register-vars(
      (
        "navbar-item-background-l-delta": #{cv.getVar(
            "navbar-item-hover-background-l-delta"
          )},
        "navbar-item-background-a": 1,
      )
    );
  }

  &:active {
    @include cv.register-vars(
      (
        "navbar-item-background-l-delta": #{cv.getVar(
            "navbar-item-active-background-l-delta"
          )},
        "navbar-item-background-a": 1,
      )
    );
  }

  &.#{iv.$class-prefix}is-active,
  &.#{iv.$class-prefix}is-selected {
    @include cv.register-vars(
      (
        "navbar-h": #{cv.getVar("navbar-item-selected-h")},
        "navbar-s": #{cv.getVar("navbar-item-selected-s")},
        "navbar-l": #{cv.getVar("navbar-item-selected-l")},
        "navbar-item-background-l": #{cv.getVar(
            "navbar-item-selected-background-l"
          )},
        "navbar-item-background-a": 1,
        "navbar-item-color-l": #{cv.getVar("navbar-item-selected-color-l")},
      )
    );
  }
}

.#{iv.$class-prefix}navbar-item {
  flex-grow: 0;
  flex-shrink: 0;

  img,
  svg {
    max-height: cv.getVar("navbar-item-img-max-height");
  }

  &.#{iv.$class-prefix}has-dropdown {
    padding: 0;
  }

  &.#{iv.$class-prefix}is-expanded {
    flex-grow: 1;
    flex-shrink: 1;
  }

  &.#{iv.$class-prefix}is-tab {
    border-bottom: 1px solid transparent;
    min-height: cv.getVar("navbar-height");
    padding-bottom: calc(0.5rem - 1px);

    &:focus,
    &:hover {
      background-color: cv.getVar("navbar-tab-hover-background-color");
      border-bottom-color: cv.getVar("navbar-tab-hover-border-bottom-color");
    }

    &.#{iv.$class-prefix}is-active {
      background-color: cv.getVar("navbar-tab-active-background-color");
      border-bottom-color: cv.getVar("navbar-tab-active-border-bottom-color");
      border-bottom-style: cv.getVar("navbar-tab-active-border-bottom-style");
      border-bottom-width: cv.getVar("navbar-tab-active-border-bottom-width");
      color: cv.getVar("navbar-tab-active-color");
      padding-bottom: calc(
        0.5rem - #{cv.getVar("navbar-tab-active-border-bottom-width")}
      );
    }
  }
}

.#{iv.$class-prefix}navbar-content {
  flex-grow: 1;
  flex-shrink: 1;
}

.#{iv.$class-prefix}navbar-link:not(.#{iv.$class-prefix}is-arrowless) {
  padding-inline-end: 2.5em;

  &::after {
    @extend %arrow;
    border-color: cv.getVar("navbar-dropdown-arrow");
    margin-top: -0.375em;
    inset-inline-end: 1.125em;
  }
}

.#{iv.$class-prefix}navbar-dropdown {
  font-size: 0.875rem;
  padding-bottom: 0.75rem;
  padding-top: 0.5rem;

  .#{iv.$class-prefix}navbar-item {
    padding-left: 1.5rem;
    padding-right: 1.5rem;

    &:not(.is-active, .is-selected) {
      background-color: hsl(
        #{cv.getVar("navbar-dropdown-item-h")},
        #{cv.getVar("navbar-dropdown-item-s")},
        calc(
          #{cv.getVar("navbar-dropdown-item-background-l")} + #{cv.getVar(
              "navbar-item-background-l-delta"
            )}
        )
      );
      color: hsl(
        #{cv.getVar("navbar-dropdown-item-h")},
        #{cv.getVar("navbar-dropdown-item-s")},
        #{cv.getVar("navbar-dropdown-item-color-l")}
      );
    }
  }
}

.#{iv.$class-prefix}navbar-divider {
  background-color: hsl(
    #{cv.getVar("navbar-h")},
    #{cv.getVar("navbar-s")},
    #{cv.getVar("navbar-divider-background-l")}
  );
  border: none;
  display: none;
  height: cv.getVar("navbar-divider-height");
  margin: 0.5rem 0;
}

@include mx.until($navbar-breakpoint) {
  .#{iv.$class-prefix}navbar > .#{iv.$class-prefix}container {
    display: block;
  }

  .#{iv.$class-prefix}navbar-brand,
  .#{iv.$class-prefix}navbar-tabs {
    .#{iv.$class-prefix}navbar-item {
      align-items: center;
      display: flex;
    }
  }

  .#{iv.$class-prefix}navbar-link {
    &::after {
      display: none;
    }
  }

  .#{iv.$class-prefix}navbar-menu {
    background-color: cv.getVar("navbar-background-color");
    box-shadow: 0 0.5em 1em
      hsla(
        #{cv.getVar("scheme-h")},
        #{cv.getVar("scheme-s")},
        #{cv.getVar("scheme-invert-l")},
        0.1
      );
    padding: 0.5rem 0;

    &.#{iv.$class-prefix}is-active {
      display: block;
    }
  }

  // Fixed navbar
  .#{iv.$class-prefix}navbar {
    &.#{iv.$class-prefix}is-fixed-bottom-touch,
    &.#{iv.$class-prefix}is-fixed-top-touch {
      @include navbar-fixed;
    }

    &.#{iv.$class-prefix}is-fixed-bottom-touch {
      bottom: 0;

      &.#{iv.$class-prefix}has-shadow {
        box-shadow: 0 -0.125em 0.1875em hsla(#{cv.getVar("scheme-h")}, #{cv.getVar(
                "scheme-s"
              )}, #{cv.getVar("scheme-invert-l")}, 0.1);
      }
    }

    &.#{iv.$class-prefix}is-fixed-top-touch {
      top: 0;
    }

    &.#{iv.$class-prefix}is-fixed-top,
    &.#{iv.$class-prefix}is-fixed-top-touch {
      .#{iv.$class-prefix}navbar-menu {
        @include mx.overflow-touch;
        max-height: calc(100vh - #{cv.getVar("navbar-height")});
        overflow: auto;
      }
    }
  }

  html,
  body {
    &.#{iv.$class-prefix}has-navbar-fixed-top-touch {
      padding-top: cv.getVar("navbar-height");
    }

    &.#{iv.$class-prefix}has-navbar-fixed-bottom-touch {
      padding-bottom: cv.getVar("navbar-height");
    }
  }
}

@include mx.from($navbar-breakpoint) {
  .#{iv.$class-prefix}navbar,
  .#{iv.$class-prefix}navbar-menu,
  .#{iv.$class-prefix}navbar-start,
  .#{iv.$class-prefix}navbar-end {
    align-items: stretch;
    display: flex;
  }

  .#{iv.$class-prefix}navbar {
    min-height: cv.getVar("navbar-height");

    &.#{iv.$class-prefix}is-spaced {
      padding: cv.getVar("navbar-padding-vertical")
        cv.getVar("navbar-padding-horizontal");

      .#{iv.$class-prefix}navbar-start,
      .#{iv.$class-prefix}navbar-end {
        align-items: center;
      }

      a.#{iv.$class-prefix}navbar-item,
      .#{iv.$class-prefix}navbar-link {
        border-radius: cv.getVar("radius");
      }
    }

    &.#{iv.$class-prefix}is-transparent {
      @include cv.register-vars(
        (
          "navbar-item-background-a": 0,
        )
      );

      .#{iv.$class-prefix}navbar-dropdown {
        a.#{iv.$class-prefix}navbar-item {
          background-color: hsl(
            #{cv.getVar("navbar-h")},
            #{cv.getVar("navbar-s")},
            calc(
              #{cv.getVar("navbar-item-background-l")} + #{cv.getVar(
                  "navbar-item-background-l-delta"
                )}
            )
          );

          &.#{iv.$class-prefix}is-active,
          &.#{iv.$class-prefix}is-selected {
            @include cv.register-vars(
              (
                "navbar-h": #{cv.getVar("navbar-item-selected-h")},
                "navbar-s": #{cv.getVar("navbar-item-selected-s")},
                "navbar-l": #{cv.getVar("navbar-item-selected-l")},
                "navbar-item-background-l": #{cv.getVar(
                    "navbar-item-selected-background-l"
                  )},
                "navbar-item-color-l": #{cv.getVar(
                    "navbar-item-selected-color-l"
                  )},
              )
            );
          }
        }
      }
    }
  }

  .#{iv.$class-prefix}navbar-burger {
    display: none;
  }

  .#{iv.$class-prefix}navbar-item,
  .#{iv.$class-prefix}navbar-link {
    align-items: center;
    display: flex;
  }

  .#{iv.$class-prefix}navbar-item {
    &.#{iv.$class-prefix}has-dropdown {
      align-items: stretch;
    }

    &.#{iv.$class-prefix}has-dropdown-up {
      .#{iv.$class-prefix}navbar-link::after {
        transform: rotate(135deg) translate(0.25em, -0.25em);
      }

      .#{iv.$class-prefix}navbar-dropdown {
        border-bottom-color: cv.getVar("navbar-dropdown-border-color");
        border-bottom-style: cv.getVar("navbar-dropdown-border-style");
        border-bottom-width: cv.getVar("navbar-dropdown-border-width");
        border-radius: cv.getVar("navbar-dropdown-radius")
          cv.getVar("navbar-dropdown-radius") 0 0;
        border-top: none;
        bottom: 100%;
        box-shadow: 0 -0.5em 0.5em hsla(#{cv.getVar("scheme-h")}, #{cv.getVar(
                "scheme-s"
              )}, #{cv.getVar("scheme-invert-l")}, 0.1);
        top: auto;
      }
    }

    &.#{iv.$class-prefix}is-active,
    &.#{iv.$class-prefix}is-hoverable:focus,
    &.#{iv.$class-prefix}is-hoverable:focus-within,
    &.#{iv.$class-prefix}is-hoverable:hover {
      .#{iv.$class-prefix}navbar-dropdown {
        display: block;

        .#{iv.$class-prefix}navbar.#{iv.$class-prefix}is-spaced &,
        &.#{iv.$class-prefix}is-boxed {
          opacity: 1;
          pointer-events: auto;
          transform: translateY(0);
        }
      }
    }
  }

  .#{iv.$class-prefix}navbar-menu {
    flex-grow: 1;
    flex-shrink: 0;
  }

  .#{iv.$class-prefix}navbar-start {
    justify-content: flex-start;
    margin-inline-end: auto;
  }

  .#{iv.$class-prefix}navbar-end {
    justify-content: flex-end;
    margin-inline-start: auto;
  }

  .#{iv.$class-prefix}navbar-dropdown {
    background-color: cv.getVar("navbar-dropdown-background-color");
    border-end-start-radius: cv.getVar("navbar-dropdown-radius");
    border-end-end-radius: cv.getVar("navbar-dropdown-radius");
    border-top-color: cv.getVar("navbar-dropdown-border-color");
    border-top-style: cv.getVar("navbar-dropdown-border-style");
    border-top-width: cv.getVar("navbar-dropdown-border-width");
    box-shadow: 0 0.5em 0.5em
      hsla(
        #{cv.getVar("scheme-h")},
        #{cv.getVar("scheme-s")},
        #{cv.getVar("scheme-invert-l")},
        0.1
      );
    display: none;
    font-size: 0.875rem;
    inset-inline-start: 0;
    min-width: 100%;
    position: absolute;
    top: 100%;
    z-index: cv.getVar("navbar-dropdown-z");

    .#{iv.$class-prefix}navbar-item {
      padding: 0.375rem 1rem;
      white-space: nowrap;
    }

    a.#{iv.$class-prefix}navbar-item {
      padding-inline-end: 3rem;

      &:not(.is-active, .is-selected) {
        background-color: hsl(
          #{cv.getVar("navbar-dropdown-item-h")},
          #{cv.getVar("navbar-dropdown-item-s")},
          calc(
            #{cv.getVar("navbar-dropdown-item-background-l")} + #{cv.getVar(
                "navbar-item-background-l-delta"
              )}
          )
        );
        color: hsl(
          #{cv.getVar("navbar-dropdown-item-h")},
          #{cv.getVar("navbar-dropdown-item-s")},
          #{cv.getVar("navbar-dropdown-item-color-l")}
        );
      }
    }

    .#{iv.$class-prefix}navbar.#{iv.$class-prefix}is-spaced &,
    &.#{iv.$class-prefix}is-boxed {
      border-radius: cv.getVar("navbar-dropdown-boxed-radius");
      border-top: none;
      box-shadow: cv.getVar("navbar-dropdown-boxed-shadow");
      display: block;
      opacity: 0;
      pointer-events: none;
      top: calc(100% + (#{cv.getVar("navbar-dropdown-offset")}));
      transform: translateY(-5px);
      transition-duration: cv.getVar("duration");
      transition-property: opacity, transform;
    }

    &.#{iv.$class-prefix}is-right {
      left: auto;
      right: 0;
    }
  }

  .#{iv.$class-prefix}navbar-divider {
    display: block;
  }

  .#{iv.$class-prefix}navbar > .#{iv.$class-prefix}container,
  .#{iv.$class-prefix}container > .#{iv.$class-prefix}navbar {
    .#{iv.$class-prefix}navbar-brand {
      margin-inline-start: -0.75rem;
    }

    .#{iv.$class-prefix}navbar-menu {
      margin-inline-end: -0.75rem;
    }
  }

  // Fixed navbar
  .#{iv.$class-prefix}navbar {
    &.#{iv.$class-prefix}is-fixed-bottom-desktop,
    &.#{iv.$class-prefix}is-fixed-top-desktop {
      @include navbar-fixed;
    }

    &.#{iv.$class-prefix}is-fixed-bottom-desktop {
      bottom: 0;

      &.#{iv.$class-prefix}has-shadow {
        box-shadow: 0 -0.125em 0.1875em hsla(#{cv.getVar("scheme-h")}, #{cv.getVar(
                "scheme-s"
              )}, #{cv.getVar("scheme-invert-l")}, 0.1);
      }
    }

    &.#{iv.$class-prefix}is-fixed-top-desktop {
      top: 0;
    }
  }

  html,
  body {
    &.#{iv.$class-prefix}has-navbar-fixed-top-desktop {
      padding-top: cv.getVar("navbar-height");
    }

    &.#{iv.$class-prefix}has-navbar-fixed-bottom-desktop {
      padding-bottom: cv.getVar("navbar-height");
    }

    &.#{iv.$class-prefix}has-spaced-navbar-fixed-top {
      padding-top: calc(
        #{cv.getVar("navbar-height")} + #{cv.getVar("navbar-padding-vertical")} *
          2
      );
    }

    &.#{iv.$class-prefix}has-spaced-navbar-fixed-bottom {
      padding-bottom: calc(
        #{cv.getVar("navbar-height")} + #{cv.getVar("navbar-padding-vertical")} *
          2
      );
    }
  }
}

// Combination

.#{iv.$class-prefix}hero {
  &.#{iv.$class-prefix}is-fullheight-with-navbar {
    min-height: calc(100vh - #{cv.getVar("navbar-height")});
  }
}

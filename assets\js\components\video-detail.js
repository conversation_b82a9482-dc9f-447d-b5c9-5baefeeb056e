// 视频详情页功能

class VideoDetail {
    constructor() {
        this.videoId = null;
        this.isLiked = false;
        this.isFavorited = false;
        this.currentChapter = 0;
        
        this.init();
    }
    
    init() {
        this.getVideoId();
        this.bindInteractionEvents();
        this.bindChapterEvents();
        this.bindPlaylistEvents();
        this.loadVideoData();
        
        console.log('视频详情页功能已初始化');
    }
    
    getVideoId() {
        const urlParams = new URLSearchParams(window.location.search);
        this.videoId = urlParams.get('id') || '1';
    }
    
    bindInteractionEvents() {
        // 点赞按钮
        const likeBtn = document.getElementById('like-btn');
        if (likeBtn) {
            likeBtn.addEventListener('click', () => {
                this.toggleLike();
            });
        }
        
        // 收藏按钮
        const favoriteBtn = document.getElementById('favorite-btn');
        if (favoriteBtn) {
            favoriteBtn.addEventListener('click', () => {
                this.toggleFavorite();
            });
        }
        
        // 分享按钮
        const shareBtn = document.getElementById('share-btn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => {
                this.shareVideo();
            });
        }
    }
    
    bindChapterEvents() {
        // 绑定章节点击事件
        const chapterItems = document.querySelectorAll('.timeline-item');
        chapterItems.forEach((item, index) => {
            item.addEventListener('click', () => {
                this.jumpToChapter(index);
            });
            
            // 添加悬停效果
            item.style.cursor = 'pointer';
            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = 'rgba(50, 115, 220, 0.1)';
            });
            
            item.addEventListener('mouseleave', () => {
                if (index !== this.currentChapter) {
                    item.style.backgroundColor = '';
                }
            });
        });
    }
    
    bindPlaylistEvents() {
        // 绑定播放列表项点击事件
        const playlistItems = document.querySelectorAll('.playlist-item');
        playlistItems.forEach((item, index) => {
            item.addEventListener('click', () => {
                this.playFromPlaylist(index);
            });
            
            // 添加悬停效果
            item.style.cursor = 'pointer';
            item.addEventListener('mouseenter', () => {
                if (!item.classList.contains('is-active')) {
                    item.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
                }
            });
            
            item.addEventListener('mouseleave', () => {
                if (!item.classList.contains('is-active')) {
                    item.style.backgroundColor = '';
                }
            });
        });
    }
    
    loadVideoData() {
        // 根据视频ID加载视频数据
        const videoData = this.getVideoDataById(this.videoId);
        if (videoData) {
            this.updateVideoInfo(videoData);
        }
        
        // 加载用户交互状态
        this.loadUserInteractions();
    }
    
    updateVideoInfo(videoData) {
        // 更新页面标题
        document.title = `${videoData.title} - 视频详情 - 建筑科技公司`;
        
        // 更新meta描述
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
            metaDescription.content = videoData.description;
        }
        
        // 更新面包屑
        const breadcrumbActive = document.querySelector('.breadcrumb .is-active a');
        if (breadcrumbActive) {
            breadcrumbActive.textContent = videoData.title;
        }
        
        // 更新视频标题
        const videoTitle = document.querySelector('.video-info .title');
        if (videoTitle) {
            videoTitle.textContent = videoData.title;
        }
        
        // 更新观看次数
        const viewCount = document.querySelector('.video-stats .fa-eye').nextElementSibling;
        if (viewCount) {
            viewCount.textContent = `${videoData.views} 次观看`;
        }
        
        // 更新发布日期
        const publishDate = document.querySelector('.video-stats .fa-calendar').nextElementSibling;
        if (publishDate) {
            publishDate.textContent = `发布于 ${videoData.date}`;
        }
        
        // 更新视频描述
        const description = document.querySelector('.video-description .content');
        if (description && videoData.fullDescription) {
            description.innerHTML = videoData.fullDescription;
        }
    }
    
    toggleLike() {
        const likeBtn = document.getElementById('like-btn');
        const likeCount = likeBtn.querySelector('span:last-child');
        let count = parseInt(likeCount.textContent);
        
        if (this.isLiked) {
            // 取消点赞
            this.isLiked = false;
            count--;
            likeBtn.classList.remove('is-primary');
            likeBtn.querySelector('i').classList.remove('fas');
            likeBtn.querySelector('i').classList.add('far');
            showNotification('已取消点赞', 'info');
        } else {
            // 点赞
            this.isLiked = true;
            count++;
            likeBtn.classList.add('is-primary');
            likeBtn.querySelector('i').classList.remove('far');
            likeBtn.querySelector('i').classList.add('fas');
            showNotification('感谢您的点赞！', 'success');
        }
        
        likeCount.textContent = count;
        
        // 保存到本地存储
        this.saveUserInteraction('like', this.isLiked);
    }
    
    toggleFavorite() {
        const favoriteBtn = document.getElementById('favorite-btn');
        const favoriteText = favoriteBtn.querySelector('span:last-child');
        
        if (this.isFavorited) {
            // 取消收藏
            this.isFavorited = false;
            favoriteBtn.classList.remove('is-danger');
            favoriteBtn.querySelector('i').classList.remove('fas');
            favoriteBtn.querySelector('i').classList.add('far');
            favoriteText.textContent = '收藏';
            showNotification('已取消收藏', 'info');
        } else {
            // 收藏
            this.isFavorited = true;
            favoriteBtn.classList.add('is-danger');
            favoriteBtn.querySelector('i').classList.remove('far');
            favoriteBtn.querySelector('i').classList.add('fas');
            favoriteText.textContent = '已收藏';
            showNotification('已添加到收藏夹', 'success');
        }
        
        // 保存到本地存储
        this.saveUserInteraction('favorite', this.isFavorited);
    }
    
    shareVideo() {
        const videoUrl = window.location.href;
        const videoTitle = document.querySelector('.video-info .title').textContent;
        
        // 检查是否支持Web Share API
        if (navigator.share) {
            navigator.share({
                title: videoTitle,
                text: '观看这个精彩的建筑技术视频',
                url: videoUrl
            }).then(() => {
                showNotification('分享成功', 'success');
            }).catch((error) => {
                console.log('分享失败:', error);
                this.fallbackShare(videoUrl, videoTitle);
            });
        } else {
            this.fallbackShare(videoUrl, videoTitle);
        }
    }
    
    fallbackShare(url, title) {
        // 复制链接到剪贴板
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(() => {
                showNotification('链接已复制到剪贴板', 'success');
            }).catch(() => {
                this.showShareModal(url, title);
            });
        } else {
            this.showShareModal(url, title);
        }
    }
    
    showShareModal(url, title) {
        // 创建分享模态框
        const modalHTML = `
            <div class="modal is-active" id="share-modal">
                <div class="modal-background"></div>
                <div class="modal-card">
                    <header class="modal-card-head">
                        <p class="modal-card-title">分享视频</p>
                        <button class="delete" aria-label="关闭"></button>
                    </header>
                    <section class="modal-card-body">
                        <div class="field">
                            <label class="label">视频链接</label>
                            <div class="control">
                                <input class="input" type="text" value="${url}" readonly>
                            </div>
                        </div>
                        <div class="buttons is-centered">
                            <button class="button is-primary" onclick="this.previousElementSibling.previousElementSibling.querySelector('input').select(); document.execCommand('copy'); showNotification('链接已复制', 'success');">
                                复制链接
                            </button>
                        </div>
                    </section>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        const modal = document.getElementById('share-modal');
        const deleteBtn = modal.querySelector('.delete');
        const background = modal.querySelector('.modal-background');
        
        deleteBtn.addEventListener('click', () => modal.remove());
        background.addEventListener('click', () => modal.remove());
    }
    
    jumpToChapter(chapterIndex) {
        // 更新当前章节
        const chapterItems = document.querySelectorAll('.timeline-item');
        chapterItems.forEach((item, index) => {
            if (index === chapterIndex) {
                item.style.backgroundColor = 'rgba(50, 115, 220, 0.2)';
                this.currentChapter = index;
            } else {
                item.style.backgroundColor = '';
            }
        });
        
        // 获取章节时间
        const timeElement = chapterItems[chapterIndex].querySelector('.timeline-date');
        const timeText = timeElement.textContent;
        
        showNotification(`跳转到: ${timeText}`, 'info');
        
        // 实际项目中这里应该控制视频播放器跳转到指定时间
        console.log(`跳转到章节 ${chapterIndex + 1}: ${timeText}`);
    }
    
    playFromPlaylist(playlistIndex) {
        // 更新播放列表活动状态
        const playlistItems = document.querySelectorAll('.playlist-item');
        playlistItems.forEach((item, index) => {
            if (index === playlistIndex) {
                item.classList.add('is-active');
                item.style.backgroundColor = 'rgba(50, 115, 220, 0.1)';
            } else {
                item.classList.remove('is-active');
                item.style.backgroundColor = '';
            }
        });
        
        const videoTitle = playlistItems[playlistIndex].querySelector('.title').textContent;
        showNotification(`正在播放: ${videoTitle}`, 'info');
        
        // 实际项目中这里应该加载新的视频
        console.log(`播放播放列表中的视频 ${playlistIndex + 1}: ${videoTitle}`);
    }
    
    saveUserInteraction(type, value) {
        const key = `video-${this.videoId}-${type}`;
        localStorage.setItem(key, JSON.stringify(value));
    }
    
    loadUserInteractions() {
        // 加载点赞状态
        const likeKey = `video-${this.videoId}-like`;
        const savedLike = localStorage.getItem(likeKey);
        if (savedLike) {
            this.isLiked = JSON.parse(savedLike);
            if (this.isLiked) {
                const likeBtn = document.getElementById('like-btn');
                likeBtn.classList.add('is-primary');
                likeBtn.querySelector('i').classList.remove('far');
                likeBtn.querySelector('i').classList.add('fas');
            }
        }
        
        // 加载收藏状态
        const favoriteKey = `video-${this.videoId}-favorite`;
        const savedFavorite = localStorage.getItem(favoriteKey);
        if (savedFavorite) {
            this.isFavorited = JSON.parse(savedFavorite);
            if (this.isFavorited) {
                const favoriteBtn = document.getElementById('favorite-btn');
                favoriteBtn.classList.add('is-danger');
                favoriteBtn.querySelector('i').classList.remove('far');
                favoriteBtn.querySelector('i').classList.add('fas');
                favoriteBtn.querySelector('span:last-child').textContent = '已收藏';
            }
        }
    }
    
    getVideoDataById(videoId) {
        // 模拟视频数据库
        const videoDatabase = {
            '1': {
                title: '玻璃幕墙施工工艺详解',
                description: '详细介绍单元式玻璃幕墙的施工工艺流程',
                views: '12,580',
                date: '2024年1月15日',
                fullDescription: `
                    <p>本视频详细介绍了单元式玻璃幕墙的施工工艺流程，从设计深化到现场安装，每一个环节都有专业的技术讲解。</p>
                    <p>内容包括：</p>
                    <ul>
                        <li>幕墙系统设计要点</li>
                        <li>材料选择和质量控制</li>
                        <li>现场测量和定位技术</li>
                        <li>单元板块制作工艺</li>
                        <li>现场安装施工流程</li>
                        <li>质量检验和验收标准</li>
                    </ul>
                    <p>通过观看本视频，您将深入了解现代幕墙技术的精髓，掌握专业的施工工艺和质量控制要点。</p>
                `
            },
            '5': {
                title: '铝合金门窗安装工艺',
                description: '专业的铝合金门窗安装工艺展示',
                views: '5,230',
                date: '2024年1月8日',
                fullDescription: `
                    <p>专业的铝合金门窗安装工艺展示，从测量定位到最终验收的完整流程。</p>
                `
            }
        };
        
        return videoDatabase[videoId] || videoDatabase['1'];
    }
    
    // 添加视频播放进度跟踪
    trackVideoProgress() {
        // 实际项目中可以跟踪用户的观看进度
        const progressKey = `video-${this.videoId}-progress`;
        const savedProgress = localStorage.getItem(progressKey);
        
        if (savedProgress) {
            const progress = JSON.parse(savedProgress);
            console.log(`上次观看到: ${progress.time}秒`);
        }
    }
    
    // 保存观看进度
    saveVideoProgress(currentTime, duration) {
        const progressKey = `video-${this.videoId}-progress`;
        const progress = {
            time: currentTime,
            duration: duration,
            percentage: (currentTime / duration) * 100,
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem(progressKey, JSON.stringify(progress));
    }
}

// 初始化视频详情页功能
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.video-info')) {
        window.videoDetail = new VideoDetail();
    }
});

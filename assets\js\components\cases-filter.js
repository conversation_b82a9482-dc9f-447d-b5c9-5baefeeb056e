// 案例筛选功能

class CasesFilter {
    constructor() {
        this.currentFilters = {
            category: 'all',
            scale: 'all',
            region: 'all',
            sort: 'latest'
        };
        this.currentPage = 1;
        this.itemsPerPage = 6;
        this.allCases = [];
        this.filteredCases = [];
        
        this.init();
    }
    
    init() {
        this.bindFilterEvents();
        this.bindPaginationEvents();
        this.bindLoadMoreEvent();
        this.loadCases();
        
        console.log('案例筛选功能已初始化');
    }
    
    bindFilterEvents() {
        // 绑定筛选选项点击事件
        const filterOptions = document.querySelectorAll('.filter-option');
        filterOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleFilterClick(option);
            });
        });
    }
    
    handleFilterClick(option) {
        const filterGroup = option.closest('.filter-group');
        const filterType = this.getFilterType(filterGroup);
        const filterValue = option.getAttribute(`data-${filterType}`);
        
        // 更新活动状态
        filterGroup.querySelectorAll('.filter-option').forEach(opt => {
            opt.classList.remove('is-active');
        });
        option.classList.add('is-active');
        
        // 更新筛选条件
        this.currentFilters[filterType] = filterValue;
        this.currentPage = 1;
        
        // 应用筛选
        this.applyFilters();
    }
    
    getFilterType(filterGroup) {
        const label = filterGroup.querySelector('.filter-label').textContent;
        switch (label) {
            case '业务类型': return 'category';
            case '项目规模': return 'scale';
            case '地区': return 'region';
            case '排序方式': return 'sort';
            default: return 'category';
        }
    }
    
    loadCases() {
        // 获取页面上的案例数据
        const caseItems = document.querySelectorAll('.case-item');
        this.allCases = Array.from(caseItems).map(item => {
            return {
                element: item,
                category: item.getAttribute('data-category'),
                scale: item.getAttribute('data-scale'),
                region: item.getAttribute('data-region'),
                date: item.getAttribute('data-date'),
                title: item.querySelector('.card-title a').textContent,
                excerpt: item.querySelector('.card-excerpt').textContent
            };
        });
        
        this.filteredCases = [...this.allCases];
        this.applyFilters();
    }
    
    applyFilters() {
        // 筛选案例
        this.filteredCases = this.allCases.filter(caseItem => {
            return this.matchesFilters(caseItem);
        });
        
        // 排序
        this.sortCases();
        
        // 显示结果
        this.displayCases();
        
        // 更新分页
        this.updatePagination();
    }
    
    matchesFilters(caseItem) {
        // 检查类别筛选
        if (this.currentFilters.category !== 'all' && 
            caseItem.category !== this.currentFilters.category) {
            return false;
        }
        
        // 检查规模筛选
        if (this.currentFilters.scale !== 'all') {
            const scale = this.getScaleFromArea(caseItem.element);
            if (scale !== this.currentFilters.scale) {
                return false;
            }
        }
        
        // 检查地区筛选
        if (this.currentFilters.region !== 'all' && 
            caseItem.region !== this.currentFilters.region) {
            return false;
        }
        
        return true;
    }
    
    getScaleFromArea(element) {
        const areaText = element.querySelector('.tag.is-light').textContent;
        const area = parseInt(areaText.replace(/[^\d]/g, ''));
        
        if (area > 30000) return 'large';
        if (area >= 10000) return 'medium';
        return 'small';
    }
    
    sortCases() {
        switch (this.currentFilters.sort) {
            case 'latest':
                this.filteredCases.sort((a, b) => b.date.localeCompare(a.date));
                break;
            case 'popular':
                // 模拟热门度排序（实际项目中可能基于浏览量等数据）
                this.filteredCases.sort(() => Math.random() - 0.5);
                break;
            case 'scale':
                this.filteredCases.sort((a, b) => {
                    const areaA = this.getAreaFromElement(a.element);
                    const areaB = this.getAreaFromElement(b.element);
                    return areaB - areaA;
                });
                break;
        }
    }
    
    getAreaFromElement(element) {
        const areaText = element.querySelector('.tag.is-light').textContent;
        return parseInt(areaText.replace(/[^\d]/g, ''));
    }
    
    displayCases() {
        const grid = document.getElementById('cases-grid');
        
        // 隐藏所有案例
        this.allCases.forEach(caseItem => {
            caseItem.element.style.display = 'none';
        });
        
        // 计算当前页显示的案例
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const casesToShow = this.filteredCases.slice(startIndex, endIndex);
        
        // 显示当前页的案例
        casesToShow.forEach((caseItem, index) => {
            caseItem.element.style.display = 'block';
            
            // 添加动画效果
            setTimeout(() => {
                caseItem.element.classList.add('fade-in', 'is-visible');
            }, index * 100);
        });
        
        // 显示结果统计
        this.showResultsCount();
    }
    
    showResultsCount() {
        const totalResults = this.filteredCases.length;
        const startIndex = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endIndex = Math.min(this.currentPage * this.itemsPerPage, totalResults);
        
        // 创建或更新结果统计显示
        let resultsInfo = document.querySelector('.results-info');
        if (!resultsInfo) {
            resultsInfo = document.createElement('div');
            resultsInfo.className = 'results-info has-text-centered mb-4';
            const grid = document.getElementById('cases-grid');
            grid.parentNode.insertBefore(resultsInfo, grid);
        }
        
        if (totalResults === 0) {
            resultsInfo.innerHTML = '<p class="has-text-grey">未找到符合条件的案例</p>';
        } else {
            resultsInfo.innerHTML = `
                <p class="has-text-grey">
                    显示第 ${startIndex}-${endIndex} 项，共 ${totalResults} 个案例
                </p>
            `;
        }
    }
    
    updatePagination() {
        const totalPages = Math.ceil(this.filteredCases.length / this.itemsPerPage);
        const pagination = document.querySelector('.pagination');
        
        if (totalPages <= 1) {
            pagination.style.display = 'none';
            return;
        }
        
        pagination.style.display = 'flex';
        
        // 更新上一页按钮
        const prevBtn = pagination.querySelector('.pagination-previous');
        if (this.currentPage === 1) {
            prevBtn.setAttribute('disabled', '');
        } else {
            prevBtn.removeAttribute('disabled');
        }
        
        // 更新下一页按钮
        const nextBtn = pagination.querySelector('.pagination-next');
        if (this.currentPage === totalPages) {
            nextBtn.setAttribute('disabled', '');
        } else {
            nextBtn.removeAttribute('disabled');
        }
        
        // 更新页码列表
        this.updatePageNumbers(totalPages);
    }
    
    updatePageNumbers(totalPages) {
        const pageList = document.querySelector('.pagination-list');
        pageList.innerHTML = '';
        
        // 计算显示的页码范围
        const maxVisible = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(totalPages, startPage + maxVisible - 1);
        
        if (endPage - startPage < maxVisible - 1) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }
        
        // 添加页码
        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement('li');
            const a = document.createElement('a');
            a.className = 'pagination-link';
            a.textContent = i;
            a.setAttribute('aria-label', `转到第${i}页`);
            
            if (i === this.currentPage) {
                a.classList.add('is-current');
                a.setAttribute('aria-current', 'page');
            }
            
            a.addEventListener('click', (e) => {
                e.preventDefault();
                this.goToPage(i);
            });
            
            li.appendChild(a);
            pageList.appendChild(li);
        }
        
        // 添加省略号和最后一页
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsis = document.createElement('li');
                ellipsis.innerHTML = '<span class="pagination-ellipsis">&hellip;</span>';
                pageList.appendChild(ellipsis);
            }
            
            const lastPage = document.createElement('li');
            const lastLink = document.createElement('a');
            lastLink.className = 'pagination-link';
            lastLink.textContent = totalPages;
            lastLink.setAttribute('aria-label', `转到第${totalPages}页`);
            lastLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.goToPage(totalPages);
            });
            lastPage.appendChild(lastLink);
            pageList.appendChild(lastPage);
        }
    }
    
    bindPaginationEvents() {
        // 绑定上一页/下一页按钮
        const prevBtn = document.querySelector('.pagination-previous');
        const nextBtn = document.querySelector('.pagination-next');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (this.currentPage > 1) {
                    this.goToPage(this.currentPage - 1);
                }
            });
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', (e) => {
                e.preventDefault();
                const totalPages = Math.ceil(this.filteredCases.length / this.itemsPerPage);
                if (this.currentPage < totalPages) {
                    this.goToPage(this.currentPage + 1);
                }
            });
        }
    }
    
    bindLoadMoreEvent() {
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreCases();
            });
        }
    }
    
    loadMoreCases() {
        // 模拟加载更多案例
        const loadMoreBtn = document.getElementById('load-more-btn');
        const originalText = loadMoreBtn.innerHTML;
        
        loadMoreBtn.innerHTML = '<span class="loading-spinner"></span> 加载中...';
        loadMoreBtn.disabled = true;
        
        setTimeout(() => {
            // 这里可以添加实际的加载逻辑
            // 目前只是恢复按钮状态
            loadMoreBtn.innerHTML = originalText;
            loadMoreBtn.disabled = false;
            
            // 显示提示信息
            showNotification('暂无更多案例', 'info');
        }, 1000);
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.displayCases();
        this.updatePagination();
        
        // 滚动到顶部
        document.querySelector('#cases-grid').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
    
    // 获取URL参数中的筛选条件
    getUrlFilters() {
        const urlParams = new URLSearchParams(window.location.search);
        const category = urlParams.get('category');
        
        if (category) {
            this.currentFilters.category = category;
            // 更新对应的筛选按钮状态
            this.updateFilterButtonState('category', category);
        }
    }
    
    updateFilterButtonState(filterType, value) {
        const filterOptions = document.querySelectorAll(`[data-${filterType}]`);
        filterOptions.forEach(option => {
            option.classList.remove('is-active');
            if (option.getAttribute(`data-${filterType}`) === value) {
                option.classList.add('is-active');
            }
        });
    }
}

// 初始化案例筛选功能
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('cases-grid')) {
        window.casesFilter = new CasesFilter();
    }
});

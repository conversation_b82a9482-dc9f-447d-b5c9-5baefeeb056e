// 建筑科技公司官网主JavaScript文件

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有功能
    initializeNavigation();
    initializeScrollEffects();
    initializeAnimations();
    initializeCarousels();
    initializeModals();
    initializeForms();
    initializeBackToTop();
    initializeLazyLoading();
    
    console.log('建筑科技公司官网已初始化完成');
});

// 导航栏功能
function initializeNavigation() {
    const navbar = document.querySelector('.navbar');
    const navbarBurger = document.querySelector('.navbar-burger');
    const navbarMenu = document.querySelector('.navbar-menu');
    
    // 移动端菜单切换
    if (navbarBurger && navbarMenu) {
        navbarBurger.addEventListener('click', function() {
            navbarBurger.classList.toggle('is-active');
            navbarMenu.classList.toggle('is-active');
        });
    }
    
    // 滚动时导航栏样式变化
    if (navbar) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('is-scrolled');
            } else {
                navbar.classList.remove('is-scrolled');
            }
        });
    }
    
    // 当前页面导航项高亮
    highlightCurrentNavItem();
}

// 高亮当前页面导航项
function highlightCurrentNavItem() {
    const currentPath = window.location.pathname;
    const navItems = document.querySelectorAll('.navbar-menu .navbar-item');
    
    navItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href && currentPath.includes(href)) {
            item.classList.add('is-active');
        }
    });
}

// 滚动效果
function initializeScrollEffects() {
    // 平滑滚动到锚点
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // 滚动时元素入场动画
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('is-visible');
            }
        });
    }, observerOptions);
    
    // 观察所有带动画类的元素
    const animatedElements = document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right');
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

// 动画初始化
function initializeAnimations() {
    // 数字计数动画
    const counters = document.querySelectorAll('.counter-number');
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000; // 2秒
        const increment = target / (duration / 16); // 60fps
        let current = 0;
        
        const updateCounter = () => {
            current += increment;
            if (current < target) {
                counter.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };
        
        // 当元素进入视口时开始计数
        const counterObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    updateCounter();
                    counterObserver.unobserve(entry.target);
                }
            });
        });
        
        counterObserver.observe(counter);
    });
}

// 轮播图功能
function initializeCarousels() {
    const carousels = document.querySelectorAll('.hero-carousel');
    
    carousels.forEach(carousel => {
        const items = carousel.querySelectorAll('.carousel-item');
        const prevBtn = carousel.querySelector('.carousel-controls.is-left .button');
        const nextBtn = carousel.querySelector('.carousel-controls.is-right .button');
        const indicators = carousel.querySelectorAll('.indicator');
        
        let currentIndex = 0;
        const totalItems = items.length;
        
        if (totalItems === 0) return;
        
        // 显示指定索引的项目
        function showItem(index) {
            items.forEach((item, i) => {
                item.classList.toggle('is-active', i === index);
            });
            
            indicators.forEach((indicator, i) => {
                indicator.classList.toggle('is-active', i === index);
            });
            
            currentIndex = index;
        }
        
        // 下一张
        function nextItem() {
            const nextIndex = (currentIndex + 1) % totalItems;
            showItem(nextIndex);
        }
        
        // 上一张
        function prevItem() {
            const prevIndex = (currentIndex - 1 + totalItems) % totalItems;
            showItem(prevIndex);
        }
        
        // 绑定事件
        if (nextBtn) nextBtn.addEventListener('click', nextItem);
        if (prevBtn) prevBtn.addEventListener('click', prevItem);
        
        indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => showItem(index));
        });
        
        // 自动播放
        setInterval(nextItem, 5000);
        
        // 初始化显示第一张
        showItem(0);
    });
}

// 模态框功能
function initializeModals() {
    const modalTriggers = document.querySelectorAll('[data-modal-target]');
    const modals = document.querySelectorAll('.modal');
    const modalCloses = document.querySelectorAll('.modal-close, .modal-card-head .delete');
    
    // 打开模态框
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function() {
            const targetId = this.getAttribute('data-modal-target');
            const modal = document.getElementById(targetId);
            if (modal) {
                modal.classList.add('is-active');
                document.body.classList.add('modal-open');
            }
        });
    });
    
    // 关闭模态框
    modalCloses.forEach(close => {
        close.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.classList.remove('is-active');
                document.body.classList.remove('modal-open');
            }
        });
    });
    
    // 点击背景关闭模态框
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.remove('is-active');
                document.body.classList.remove('modal-open');
            }
        });
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const activeModal = document.querySelector('.modal.is-active');
            if (activeModal) {
                activeModal.classList.remove('is-active');
                document.body.classList.remove('modal-open');
            }
        }
    });
}

// 表单功能
function initializeForms() {
    const forms = document.querySelectorAll('form[data-validate]');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (validateForm(this)) {
                // 表单验证通过，可以提交
                submitForm(this);
            }
        });
    });
}

// 表单验证
function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        const value = field.value.trim();
        const fieldContainer = field.closest('.field');
        
        // 移除之前的错误状态
        field.classList.remove('is-danger');
        const existingHelp = fieldContainer.querySelector('.help.is-danger');
        if (existingHelp) {
            existingHelp.remove();
        }
        
        // 验证字段
        if (!value) {
            showFieldError(field, '此字段为必填项');
            isValid = false;
        } else if (field.type === 'email' && !isValidEmail(value)) {
            showFieldError(field, '请输入有效的邮箱地址');
            isValid = false;
        } else if (field.type === 'tel' && !isValidPhone(value)) {
            showFieldError(field, '请输入有效的电话号码');
            isValid = false;
        }
    });
    
    return isValid;
}

// 显示字段错误
function showFieldError(field, message) {
    field.classList.add('is-danger');
    const fieldContainer = field.closest('.field');
    const helpElement = document.createElement('p');
    helpElement.className = 'help is-danger';
    helpElement.textContent = message;
    fieldContainer.appendChild(helpElement);
}

// 邮箱验证
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 电话验证
function isValidPhone(phone) {
    const phoneRegex = /^[\d\s\-\+\(\)]+$/;
    return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
}

// 提交表单
function submitForm(form) {
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    // 显示加载状态
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="loading-spinner"></span> 提交中...';
    
    // 模拟表单提交
    setTimeout(() => {
        // 恢复按钮状态
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
        
        // 显示成功消息
        showNotification('表单提交成功！我们会尽快与您联系。', 'success');
        
        // 重置表单
        form.reset();
    }, 2000);
}

// 返回顶部按钮
function initializeBackToTop() {
    const backToTopBtn = document.querySelector('.back-to-top');
    
    if (backToTopBtn) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('is-visible');
            } else {
                backToTopBtn.classList.remove('is-visible');
            }
        });
        
        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// 懒加载图片
function initializeLazyLoading() {
    const lazyImages = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.getAttribute('data-src');
                img.removeAttribute('data-src');
                img.classList.add('loaded');
                imageObserver.unobserve(img);
            }
        });
    });
    
    lazyImages.forEach(img => {
        imageObserver.observe(img);
    });
}

// 显示通知消息
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification is-${type}`;
    notification.innerHTML = `
        <button class="delete"></button>
        ${message}
    `;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 定位
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.maxWidth = '400px';
    
    // 关闭按钮事件
    const deleteBtn = notification.querySelector('.delete');
    deleteBtn.addEventListener('click', function() {
        notification.remove();
    });
    
    // 自动关闭
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

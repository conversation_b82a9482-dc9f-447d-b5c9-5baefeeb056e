// 建筑科技公司官网组件样式

// 导航栏组件
.navbar {
  transition: $theme-transition;
  backdrop-filter: $backdrop-blur;
  
  &.is-scrolled {
    background-color: rgba($white, 0.95);
    box-shadow: $shadow;
  }
  
  .navbar-brand {
    .navbar-item {
      img {
        max-height: 2.5rem;
      }
    }
  }
  
  .navbar-menu {
    .navbar-item {
      transition: color $duration-medium ease;
      
      &:hover {
        color: $primary;
      }
      
      &.is-active {
        color: $primary;
        font-weight: $weight-semibold;
      }
    }
  }
  
  .theme-toggle {
    background: none;
    border: none;
    color: $grey-dark;
    font-size: 1.2rem;
    cursor: pointer;
    transition: color $duration-medium ease;
    
    &:hover {
      color: $primary;
    }
  }
}

// Hero 区域
.hero {
  position: relative;
  overflow: hidden;
  
  &.is-fullheight-with-navbar {
    min-height: calc(100vh - #{$navbar-height});
  }
  
  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
  }
  
  .hero-body {
    position: relative;
    z-index: 2;
    
    .hero-content {
      text-align: center;
      
      .title {
        text-shadow: $shadow;
        margin-bottom: $spacing-lg;
      }
      
      .subtitle {
        text-shadow: $shadow-light;
        margin-bottom: $spacing-xl;
      }
    }
  }
  
  .hero-carousel {
    position: relative;
    
    .carousel-item {
      display: none;
      
      &.is-active {
        display: block;
      }
    }
    
    .carousel-controls {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 3;
      
      &.is-left {
        left: $spacing-lg;
      }
      
      &.is-right {
        right: $spacing-lg;
      }
      
      .button {
        background: rgba($white, 0.2);
        border: 1px solid rgba($white, 0.3);
        color: $white;
        
        &:hover {
          background: rgba($white, 0.3);
        }
      }
    }
    
    .carousel-indicators {
      position: absolute;
      bottom: $spacing-lg;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: $spacing-sm;
      z-index: 3;
      
      .indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: rgba($white, 0.5);
        border: none;
        cursor: pointer;
        transition: background-color $duration-medium ease;
        
        &.is-active {
          background: $white;
        }
      }
    }
  }
}

// 卡片组件增强
.card {
  border-radius: $radius-large;
  overflow: hidden;
  
  .card-image {
    position: relative;
    overflow: hidden;
    
    img {
      transition: transform $duration-long ease;
    }
    
    .card-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba($black, 0.4);
      opacity: 0;
      transition: opacity $duration-medium ease;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .overlay-content {
        color: $white;
        text-align: center;
        transform: translateY(20px);
        transition: transform $duration-medium ease;
      }
    }
  }
  
  &:hover {
    .card-image {
      img {
        transform: scale(1.05);
      }
      
      .card-overlay {
        opacity: 1;
        
        .overlay-content {
          transform: translateY(0);
        }
      }
    }
  }
  
  .card-content {
    .card-title {
      margin-bottom: $spacing-sm;
      font-weight: $weight-semibold;
    }
    
    .card-meta {
      font-size: $size-7;
      color: $grey;
      margin-bottom: $spacing-sm;
    }
    
    .card-excerpt {
      line-height: 1.6;
      margin-bottom: $spacing-md;
    }
    
    .card-tags {
      .tag {
        margin-right: $spacing-xs;
        margin-bottom: $spacing-xs;
      }
    }
  }
}

// 统计数字组件
.stats-counter {
  text-align: center;
  
  .counter-number {
    font-size: $size-1;
    font-weight: $weight-bold;
    color: $primary;
    display: block;
    line-height: 1;
  }
  
  .counter-label {
    font-size: $size-6;
    color: $grey;
    margin-top: $spacing-xs;
  }
}

// 时间线组件
.timeline {
  position: relative;
  padding-left: $spacing-xl;
  
  &::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: $grey-lighter;
  }
  
  .timeline-item {
    position: relative;
    margin-bottom: $spacing-xl;
    
    &::before {
      content: '';
      position: absolute;
      left: -23px;
      top: 8px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: $primary;
      border: 3px solid $white;
      box-shadow: $shadow;
    }
    
    .timeline-content {
      background: $white;
      padding: $spacing-lg;
      border-radius: $radius;
      box-shadow: $shadow-light;
      
      .timeline-date {
        font-size: $size-7;
        color: $primary;
        font-weight: $weight-semibold;
        margin-bottom: $spacing-xs;
      }
      
      .timeline-title {
        font-size: $size-5;
        font-weight: $weight-semibold;
        margin-bottom: $spacing-sm;
      }
      
      .timeline-description {
        color: $grey-dark;
        line-height: 1.6;
      }
    }
  }
}

// 筛选器组件
.filter-bar {
  background: $white-ter;
  padding: $spacing-lg;
  border-radius: $radius;
  margin-bottom: $spacing-xl;
  
  .filter-group {
    margin-bottom: $spacing-md;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .filter-label {
      font-weight: $weight-semibold;
      margin-bottom: $spacing-sm;
      display: block;
    }
    
    .filter-options {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-sm;
      
      .filter-option {
        padding: $spacing-xs $spacing-md;
        border: 1px solid $grey-lighter;
        border-radius: $radius;
        background: $white;
        color: $grey-dark;
        text-decoration: none;
        transition: all $duration-medium ease;
        font-size: $size-7;
        
        &:hover {
          border-color: $primary;
          color: $primary;
        }
        
        &.is-active {
          background: $primary;
          border-color: $primary;
          color: $white;
        }
      }
    }
  }
}

// 分页组件
.pagination {
  .pagination-link {
    transition: all $duration-medium ease;
    
    &:hover {
      transform: translateY(-1px);
    }
  }
}

// 面包屑导航
.breadcrumb {
  margin-bottom: $spacing-lg;
  
  a {
    transition: color $duration-medium ease;
    
    &:hover {
      color: $primary;
    }
  }
}

// 返回顶部按钮
.back-to-top {
  position: fixed;
  bottom: $spacing-lg;
  right: $spacing-lg;
  width: 50px;
  height: 50px;
  background: $primary;
  color: $white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all $duration-medium ease;
  z-index: $z-fixed;
  
  &.is-visible {
    opacity: 1;
    visibility: visible;
  }
  
  &:hover {
    background: $primary-dark;
    transform: translateY(-2px);
  }
}

// 加载更多按钮
.load-more {
  text-align: center;
  margin-top: $spacing-xl;
  
  .button {
    min-width: 200px;
  }
}

<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="建筑科技公司成功案例展示，包括玻璃幕墙和铝合金门窗项目案例，展现我们的专业实力和丰富经验">
    <meta name="keywords" content="建筑案例,玻璃幕墙案例,铝合金门窗案例,工程案例,建筑项目">
    <meta name="author" content="建筑科技公司">
    <meta name="theme-color" content="#3273dc">
    
    <title>成功案例 - 建筑科技公司</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/main.css">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="assets/js/main.js" as="script">
    <link rel="preload" href="assets/js/theme-switch.js" as="script">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar is-fixed-top" role="navigation" aria-label="主导航">
        <div class="container">
            <div class="navbar-brand">
                <a class="navbar-item" href="index.html">
                    <img src="assets/images/logo/logo.png" alt="建筑科技公司" width="120" height="40">
                </a>
                
                <a role="button" class="navbar-burger" aria-label="菜单" aria-expanded="false" data-target="navbarMenu">
                    <span aria-hidden="true"></span>
                    <span aria-hidden="true"></span>
                    <span aria-hidden="true"></span>
                </a>
            </div>
            
            <div id="navbarMenu" class="navbar-menu">
                <div class="navbar-start">
                    <a class="navbar-item" href="index.html">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </a>
                    
                    <div class="navbar-item has-dropdown is-hoverable">
                        <a class="navbar-link">
                            <i class="fas fa-building"></i>
                            <span>产品中心</span>
                        </a>
                        <div class="navbar-dropdown">
                            <a class="navbar-item" href="glass-curtain-wall.html">
                                <i class="fas fa-square"></i>
                                <span>玻璃幕墙</span>
                            </a>
                            <a class="navbar-item" href="aluminum-doors-windows.html">
                                <i class="fas fa-door-open"></i>
                                <span>铝合金门窗</span>
                            </a>
                        </div>
                    </div>
                    
                    <a class="navbar-item is-active" href="cases.html">
                        <i class="fas fa-images"></i>
                        <span>成功案例</span>
                    </a>
                    
                    <a class="navbar-item" href="videos.html">
                        <i class="fas fa-video"></i>
                        <span>视频中心</span>
                    </a>
                    
                    <a class="navbar-item" href="news.html">
                        <i class="fas fa-newspaper"></i>
                        <span>资讯动态</span>
                    </a>
                    
                    <a class="navbar-item" href="about.html">
                        <i class="fas fa-info-circle"></i>
                        <span>关于我们</span>
                    </a>
                    
                    <a class="navbar-item" href="contact.html">
                        <i class="fas fa-phone"></i>
                        <span>联系我们</span>
                    </a>
                </div>
                
                <div class="navbar-end">
                    <div class="navbar-item">
                        <div class="buttons">
                            <a class="button is-light" href="search.html" aria-label="搜索">
                                <i class="fas fa-search"></i>
                            </a>
                            <button class="button is-light theme-toggle" aria-label="切换主题">
                                <i class="fas fa-moon"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <main>
        <!-- 面包屑导航 -->
        <section class="section is-small">
            <div class="container">
                <nav class="breadcrumb" aria-label="breadcrumbs">
                    <ul>
                        <li><a href="index.html">首页</a></li>
                        <li class="is-active"><a href="#" aria-current="page">成功案例</a></li>
                    </ul>
                </nav>
            </div>
        </section>
        
        <!-- 页面标题区 -->
        <section class="hero is-medium">
            <div class="hero-body">
                <div class="container">
                    <div class="hero-content has-text-centered fade-in">
                        <h1 class="title is-1">成功案例</h1>
                        <p class="subtitle is-3">品质工程，见证实力</p>
                        <p class="content is-medium">
                            多年来，我们成功完成了众多玻璃幕墙和铝合金门窗项目，
                            涵盖商业建筑、住宅建筑、公共建筑等各个领域，
                            每一个项目都体现了我们的专业实力和品质追求。
                        </p>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 筛选区 -->
        <section class="section">
            <div class="container">
                <div class="filter-bar">
                    <div class="filter-group">
                        <label class="filter-label">业务类型</label>
                        <div class="filter-options">
                            <a href="#" class="filter-option is-active" data-filter="all">全部</a>
                            <a href="#" class="filter-option" data-filter="glass-curtain-wall">玻璃幕墙</a>
                            <a href="#" class="filter-option" data-filter="aluminum-doors-windows">铝合金门窗</a>
                        </div>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">项目规模</label>
                        <div class="filter-options">
                            <a href="#" class="filter-option is-active" data-scale="all">全部</a>
                            <a href="#" class="filter-option" data-scale="large">大型项目 (>30,000㎡)</a>
                            <a href="#" class="filter-option" data-scale="medium">中型项目 (10,000-30,000㎡)</a>
                            <a href="#" class="filter-option" data-scale="small">小型项目 (<10,000㎡)</a>
                        </div>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">地区</label>
                        <div class="filter-options">
                            <a href="#" class="filter-option is-active" data-region="all">全部</a>
                            <a href="#" class="filter-option" data-region="beijing">北京</a>
                            <a href="#" class="filter-option" data-region="shanghai">上海</a>
                            <a href="#" class="filter-option" data-region="guangzhou">广州</a>
                            <a href="#" class="filter-option" data-region="shenzhen">深圳</a>
                            <a href="#" class="filter-option" data-region="other">其他</a>
                        </div>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">排序方式</label>
                        <div class="filter-options">
                            <a href="#" class="filter-option is-active" data-sort="latest">最新项目</a>
                            <a href="#" class="filter-option" data-sort="popular">热门项目</a>
                            <a href="#" class="filter-option" data-sort="scale">项目规模</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 案例网格 -->
        <section class="section">
            <div class="container">
                <div class="columns is-multiline" id="cases-grid">
                    <!-- 案例卡片将通过JavaScript动态加载 -->
                    <div class="column is-4 case-item" data-category="glass-curtain-wall" data-scale="large" data-region="beijing" data-date="2023-12">
                        <div class="card">
                            <div class="card-image">
                                <figure class="image is-16by9">
                                    <img src="assets/images/cases/case-1.jpg" alt="北京国际金融中心" loading="lazy">
                                </figure>
                                <div class="card-overlay">
                                    <div class="overlay-content">
                                        <h3 class="title is-4 has-text-white">查看详情</h3>
                                        <p class="has-text-white">了解项目详细信息</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">
                                    <a href="case-detail.html?id=1">北京国际金融中心</a>
                                </h3>
                                <p class="card-meta">
                                    <span class="tag is-primary">玻璃幕墙</span>
                                    <span class="tag is-light">50,000㎡</span>
                                    <span class="tag is-info">2023年</span>
                                </p>
                                <p class="card-excerpt">
                                    采用先进的单元式玻璃幕墙技术，总面积达50,000平方米，
                                    为这座地标性建筑提供了完美的外立面解决方案。
                                </p>
                                <div class="card-footer">
                                    <div class="level">
                                        <div class="level-left">
                                            <div class="level-item">
                                                <span class="icon-text">
                                                    <span class="icon">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                    </span>
                                                    <span>北京</span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="level-right">
                                            <div class="level-item">
                                                <a href="case-detail.html?id=1" class="button is-primary is-small">
                                                    <span>查看详情</span>
                                                    <span class="icon">
                                                        <i class="fas fa-arrow-right"></i>
                                                    </span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="column is-4 case-item" data-category="aluminum-doors-windows" data-scale="medium" data-region="shanghai" data-date="2023-11">
                        <div class="card">
                            <div class="card-image">
                                <figure class="image is-16by9">
                                    <img src="assets/images/cases/case-2.jpg" alt="上海商业综合体" loading="lazy">
                                </figure>
                                <div class="card-overlay">
                                    <div class="overlay-content">
                                        <h3 class="title is-4 has-text-white">查看详情</h3>
                                        <p class="has-text-white">了解项目详细信息</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">
                                    <a href="case-detail.html?id=2">上海商业综合体</a>
                                </h3>
                                <p class="card-meta">
                                    <span class="tag is-info">铝合金门窗</span>
                                    <span class="tag is-light">15,000㎡</span>
                                    <span class="tag is-info">2023年</span>
                                </p>
                                <p class="card-excerpt">
                                    高端铝合金门窗系统，具有优异的保温隔音性能，
                                    为商业空间提供了舒适的室内环境。
                                </p>
                                <div class="card-footer">
                                    <div class="level">
                                        <div class="level-left">
                                            <div class="level-item">
                                                <span class="icon-text">
                                                    <span class="icon">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                    </span>
                                                    <span>上海</span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="level-right">
                                            <div class="level-item">
                                                <a href="case-detail.html?id=2" class="button is-primary is-small">
                                                    <span>查看详情</span>
                                                    <span class="icon">
                                                        <i class="fas fa-arrow-right"></i>
                                                    </span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="column is-4 case-item" data-category="glass-curtain-wall" data-scale="large" data-region="shenzhen" data-date="2023-10">
                        <div class="card">
                            <div class="card-image">
                                <figure class="image is-16by9">
                                    <img src="assets/images/cases/case-3.jpg" alt="深圳科技园区" loading="lazy">
                                </figure>
                                <div class="card-overlay">
                                    <div class="overlay-content">
                                        <h3 class="title is-4 has-text-white">查看详情</h3>
                                        <p class="has-text-white">了解项目详细信息</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">
                                    <a href="case-detail.html?id=3">深圳科技园区</a>
                                </h3>
                                <p class="card-meta">
                                    <span class="tag is-primary">玻璃幕墙</span>
                                    <span class="tag is-light">35,000㎡</span>
                                    <span class="tag is-info">2023年</span>
                                </p>
                                <p class="card-excerpt">
                                    创新的点支式玻璃幕墙设计，展现了现代科技建筑的
                                    简洁美感和卓越性能。
                                </p>
                                <div class="card-footer">
                                    <div class="level">
                                        <div class="level-left">
                                            <div class="level-item">
                                                <span class="icon-text">
                                                    <span class="icon">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                    </span>
                                                    <span>深圳</span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="level-right">
                                            <div class="level-item">
                                                <a href="case-detail.html?id=3" class="button is-primary is-small">
                                                    <span>查看详情</span>
                                                    <span class="icon">
                                                        <i class="fas fa-arrow-right"></i>
                                                    </span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 更多案例 -->
                    <div class="column is-4 case-item" data-category="aluminum-doors-windows" data-scale="medium" data-region="guangzhou" data-date="2023-09">
                        <div class="card">
                            <div class="card-image">
                                <figure class="image is-16by9">
                                    <img src="assets/images/cases/case-4.jpg" alt="广州商业综合体" loading="lazy">
                                </figure>
                                <div class="card-overlay">
                                    <div class="overlay-content">
                                        <h3 class="title is-4 has-text-white">查看详情</h3>
                                        <p class="has-text-white">了解项目详细信息</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">
                                    <a href="case-detail.html?id=4">广州商业综合体</a>
                                </h3>
                                <p class="card-meta">
                                    <span class="tag is-info">铝合金门窗</span>
                                    <span class="tag is-light">28,000㎡</span>
                                    <span class="tag is-info">2023年</span>
                                </p>
                                <p class="card-excerpt">
                                    经济实用的框架式幕墙，满足商业建筑的功能需求，
                                    为商业空间提供良好的采光和通风。
                                </p>
                                <div class="card-footer">
                                    <div class="level">
                                        <div class="level-left">
                                            <div class="level-item">
                                                <span class="icon-text">
                                                    <span class="icon">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                    </span>
                                                    <span>广州</span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="level-right">
                                            <div class="level-item">
                                                <a href="case-detail.html?id=4" class="button is-primary is-small">
                                                    <span>查看详情</span>
                                                    <span class="icon">
                                                        <i class="fas fa-arrow-right"></i>
                                                    </span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="column is-4 case-item" data-category="aluminum-doors-windows" data-scale="small" data-region="other" data-date="2023-08">
                        <div class="card">
                            <div class="card-image">
                                <figure class="image is-16by9">
                                    <img src="assets/images/cases/case-5.jpg" alt="杭州高端住宅" loading="lazy">
                                </figure>
                                <div class="card-overlay">
                                    <div class="overlay-content">
                                        <h3 class="title is-4 has-text-white">查看详情</h3>
                                        <p class="has-text-white">了解项目详细信息</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">
                                    <a href="case-detail.html?id=5">杭州高端住宅</a>
                                </h3>
                                <p class="card-meta">
                                    <span class="tag is-info">铝合金门窗</span>
                                    <span class="tag is-light">8,000㎡</span>
                                    <span class="tag is-info">2023年</span>
                                </p>
                                <p class="card-excerpt">
                                    断桥铝门窗系统，为住宅提供舒适的居住环境，
                                    优异的保温隔音性能深受业主好评。
                                </p>
                                <div class="card-footer">
                                    <div class="level">
                                        <div class="level-left">
                                            <div class="level-item">
                                                <span class="icon-text">
                                                    <span class="icon">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                    </span>
                                                    <span>杭州</span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="level-right">
                                            <div class="level-item">
                                                <a href="case-detail.html?id=5" class="button is-primary is-small">
                                                    <span>查看详情</span>
                                                    <span class="icon">
                                                        <i class="fas fa-arrow-right"></i>
                                                    </span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="column is-4 case-item" data-category="glass-curtain-wall" data-scale="medium" data-region="other" data-date="2023-07">
                        <div class="card">
                            <div class="card-image">
                                <figure class="image is-16by9">
                                    <img src="assets/images/cases/case-6.jpg" alt="成都办公大楼" loading="lazy">
                                </figure>
                                <div class="card-overlay">
                                    <div class="overlay-content">
                                        <h3 class="title is-4 has-text-white">查看详情</h3>
                                        <p class="has-text-white">了解项目详细信息</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">
                                    <a href="case-detail.html?id=6">成都办公大楼</a>
                                </h3>
                                <p class="card-meta">
                                    <span class="tag is-primary">玻璃幕墙</span>
                                    <span class="tag is-light">22,000㎡</span>
                                    <span class="tag is-info">2023年</span>
                                </p>
                                <p class="card-excerpt">
                                    节能玻璃幕墙系统，有效降低建筑能耗，
                                    为办公环境提供良好的采光和视野。
                                </p>
                                <div class="card-footer">
                                    <div class="level">
                                        <div class="level-left">
                                            <div class="level-item">
                                                <span class="icon-text">
                                                    <span class="icon">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                    </span>
                                                    <span>成都</span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="level-right">
                                            <div class="level-item">
                                                <a href="case-detail.html?id=6" class="button is-primary is-small">
                                                    <span>查看详情</span>
                                                    <span class="icon">
                                                        <i class="fas fa-arrow-right"></i>
                                                    </span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载更多 -->
                <div class="load-more">
                    <button class="button is-primary is-large" id="load-more-btn">
                        <span>加载更多案例</span>
                        <span class="icon">
                            <i class="fas fa-chevron-down"></i>
                        </span>
                    </button>
                </div>

                <!-- 分页导航 -->
                <nav class="pagination is-centered" role="navigation" aria-label="分页导航">
                    <a class="pagination-previous" disabled>上一页</a>
                    <a class="pagination-next">下一页</a>
                    <ul class="pagination-list">
                        <li><a class="pagination-link is-current" aria-label="第1页" aria-current="page">1</a></li>
                        <li><a class="pagination-link" aria-label="转到第2页">2</a></li>
                        <li><a class="pagination-link" aria-label="转到第3页">3</a></li>
                        <li><span class="pagination-ellipsis">&hellip;</span></li>
                        <li><a class="pagination-link" aria-label="转到第8页">8</a></li>
                    </ul>
                </nav>
            </div>
        </section>
    </main>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="columns">
                <div class="column is-4">
                    <div class="content">
                        <h4 class="title is-5 has-text-white">关于我们</h4>
                        <p class="has-text-light">
                            我们是一家专业的建筑科技公司，专注于玻璃幕墙和铝合金门窗的设计、制造与安装。
                            凭借多年的行业经验和先进的技术，为客户提供高品质的建筑解决方案。
                        </p>
                        <div class="buttons">
                            <a class="button is-light is-small" href="#" aria-label="微信">
                                <i class="fab fa-weixin"></i>
                            </a>
                            <a class="button is-light is-small" href="#" aria-label="微博">
                                <i class="fab fa-weibo"></i>
                            </a>
                            <a class="button is-light is-small" href="#" aria-label="QQ">
                                <i class="fab fa-qq"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="column is-2">
                    <div class="content">
                        <h4 class="title is-5 has-text-white">产品中心</h4>
                        <ul class="has-text-light">
                            <li><a href="glass-curtain-wall.html" class="has-text-light">玻璃幕墙</a></li>
                            <li><a href="aluminum-doors-windows.html" class="has-text-light">铝合金门窗</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="column is-2">
                    <div class="content">
                        <h4 class="title is-5 has-text-white">服务支持</h4>
                        <ul class="has-text-light">
                            <li><a href="cases.html" class="has-text-light">成功案例</a></li>
                            <li><a href="videos.html" class="has-text-light">视频中心</a></li>
                            <li><a href="news.html" class="has-text-light">资讯动态</a></li>
                            <li><a href="tags.html" class="has-text-light">标签云</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="column is-4">
                    <div class="content">
                        <h4 class="title is-5 has-text-white">联系方式</h4>
                        <div class="has-text-light">
                            <p>
                                <i class="fas fa-map-marker-alt"></i>
                                <span>地址：北京市朝阳区建筑科技大厦</span>
                            </p>
                            <p>
                                <i class="fas fa-phone"></i>
                                <span>电话：************</span>
                            </p>
                            <p>
                                <i class="fas fa-envelope"></i>
                                <span>邮箱：<EMAIL></span>
                            </p>
                            <p>
                                <i class="fas fa-clock"></i>
                                <span>工作时间：周一至周五 9:00-18:00</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <hr class="has-background-grey">
            
            <div class="level">
                <div class="level-left">
                    <div class="level-item">
                        <p class="has-text-light">
                            © 2024 建筑科技公司. 保留所有权利.
                        </p>
                    </div>
                </div>
                <div class="level-right">
                    <div class="level-item">
                        <p class="has-text-light">
                            <a href="#" class="has-text-light">隐私政策</a> |
                            <a href="#" class="has-text-light">使用条款</a> |
                            <a href="sitemap.html" class="has-text-light">网站地图</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- 返回顶部按钮 -->
    <button class="back-to-top" aria-label="返回顶部">
        <i class="fas fa-chevron-up"></i>
    </button>
    
    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/ScrollTrigger.min.js"></script>
    <script src="assets/js/theme-switch.js"></script>
    <script src="assets/js/animation.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/components/cases-filter.js"></script>
</body>
</html>

// 建筑科技公司官网主样式文件
// 基于 Bulma CSS 框架

// 导入 Bulma 核心 - 使用新的模块系统
@use '../../node_modules/bulma/sass' with (
  $primary: #3273dc,
  $family-sans-serif: (-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif)
);

// 导入 Font Awesome
@import '../../node_modules/font-awesome-sass/assets/stylesheets/font-awesome';

// 导入变量定义
@import 'variables';

// 导入自定义组件样式
@import 'components';

// 导入主题样式
@import 'themes/light';
@import 'themes/dark';

// 全局样式
html {
  scroll-behavior: smooth;
}

body {
  font-family: $family-sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

// 自定义滚动条样式
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: $grey-lighter;
}

::-webkit-scrollbar-thumb {
  background: $grey;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: $grey-dark;
}

// 通用动画类
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
  
  &.is-visible {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
  
  &.is-visible {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  opacity: 0;
  transform: translateX(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
  
  &.is-visible {
    opacity: 1;
    transform: translateX(0);
  }
}

// 响应式图片
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
}

// 卡片悬停效果
.card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

// 按钮增强样式
.button {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
}

// 加载动画
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba($primary, 0.3);
  border-radius: 50%;
  border-top-color: $primary;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

// 工具类
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.bg-overlay {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
  }
  
  > * {
    position: relative;
    z-index: 2;
  }
}

// 视差效果容器
.parallax-container {
  overflow: hidden;
  position: relative;
}

.parallax-element {
  will-change: transform;
}

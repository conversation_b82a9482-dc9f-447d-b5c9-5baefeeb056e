# 建筑科技公司官网开发文档

## 1. 项目概述

### 1.1 项目背景
本项目是为一家专注于玻璃幕墙和铝合金门窗业务的现代化建筑科技公司开发的企业官网，旨在展示公司产品、案例、品牌形象及提供联系方式。

### 1.2 项目目标
- 打造视觉现代、交互流畅的企业展示平台
- 突出公司核心业务（玻璃幕墙和铝合金门窗）
- 提供完善的案例、资讯展示功能
- 支持明暗主题切换，提升用户体验
- 确保跨设备兼容性和良好的性能表现

### 1.3 目标用户
- 建筑行业从业者（建筑师、设计师、施工单位）
- 房地产开发商
- 有建筑装修需求的企业和个人
- 行业合作伙伴及求职者

## 2. 技术栈

### 2.1 核心技术
- **Bulma Sass v1.0.4**：作为主要CSS框架，提供响应式布局和组件
- **Font Awesome**：提供图标支持
- **GSAP**：用于实现高性能动画和交互效果
- **HTML5**：语义化页面结构
- **JavaScript (ES6+)**：交互逻辑实现
- **Sass**：CSS预处理器，用于样式定制和主题实现

### 2.2 开发环境
- 代码编辑器：VS Code（推荐）
- 版本控制：Git
- 构建工具：npm/yarn + webpack（可选，用于Sass编译和资源打包）
- 浏览器：Chrome, Firefox, Safari, Edge（最新版本）

## 3. 网站架构

### 3.1 页面结构
共包含15个HTML页面，具体如下：

1. `index.html` - 网站首页
2. `glass-curtain-wall.html` - 玻璃幕墙专题页
3. `aluminum-doors-windows.html` - 铝合金门窗专题页
4. `cases.html` - 成功案例列表
5. `case-detail.html` - 案例详情页
6. `videos.html` - 视频中心
7. `video-detail.html` - 视频详情页
8. `news.html` - 资讯动态
9. `news-detail.html` - 资讯详情页
10. `tags.html` - 标签云页面
11. `tag-results.html` - 标签结果页
12. `contact.html` - 联系我们
13. `about.html` - 关于我们
14. `search.html` - 搜索页面
15. `404.html` - 404错误页

### 3.2 目录结构
```
project-root/
├── index.html
├── glass-curtain-wall.html
├── aluminum-doors-windows.html
├── cases.html
├── case-detail.html
├── videos.html
├── video-detail.html
├── news.html
├── news-detail.html
├── tags.html
├── tag-results.html
├── contact.html
├── about.html
├── search.html
├── search-results.html
├── 404.html
├── assets/
│   ├── css/
│   │   ├── main.css         # 编译后的主CSS文件
│   │   └── themes/
│   │       ├── light.css    # 明主题样式
│   │       └── dark.css     # 暗主题样式
│   ├── js/
│   │   ├── main.js          # 主JavaScript文件
│   │   ├── theme-switch.js  # 主题切换功能
│   │   ├── animation.js     # GSAP动画实现
│   │   └── components/      # 各组件JS
│   ├── sass/
│   │   ├── main.scss        # 主Sass文件
│   │   ├── _variables.scss  # 变量定义
│   │   ├── _components.scss # 组件样式
│   │   └── themes/          # 主题相关Sass
│   ├── images/
│   │   ├── logo/
│   │   ├── products/
│   │   ├── cases/
│   │   ├── banners/
│   │   └── icons/
│   └── fonts/               # 自定义字体（如有）
└── favicon.ico
```

## 4. 设计规范

### 4.1 颜色方案
- **主色调**：采用Bulma原生link主要色，代表科技感与专业性

### 4.2 排版规范
- **字体**：
  - 主要字体：系统无衬线字体栈（`-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif`）
  - 代码字体：等宽字体栈（`monospace`）

### 4.3 组件设计规范
- **按钮**：
  - 主要按钮：使用主色调背景，白色文字
  - 次要按钮：白色背景，主色调边框和文字
  - 尺寸：默认、中等、大、小四种尺寸
  - 圆角：统一使用Bulma默认的轻微圆角
- **卡片**：
  - 用于展示案例、产品、资讯等内容
  - 包含图片、标题、简介、链接
  - 悬停效果：轻微阴影增强和微小上移
- **表单元素**：
  - 统一使用Bulma表单样式
  - 焦点状态：主色调边框高亮
  - 验证状态：成功/错误状态对应辅助色
- **导航**：
  - 顶部固定导航栏，滚动时变化样式
  - 移动端转为汉堡菜单
  - 当前页面导航项高亮显示

## 5. 页面设计与功能

### 5.1 公共组件
所有页面共享以下公共组件：

1. **头部导航**：
   - 公司Logo
   - 主导航菜单
   - 搜索按钮
   - 主题切换按钮（明暗模式）
   - 移动端汉堡菜单

2. **页脚**：
   - 公司简介
   - 联系方式
   - 导航链接
   - 社交媒体图标
   - 版权信息
   - 隐私政策链接

### 5.2 页面详细设计

#### 5.2.1 首页（index.html）
- **Hero区域**：
  - 全屏宽度横幅，展示公司核心业务
  - 简洁有力的标语
  - 行动号召按钮（CTA）
  - 左右切换按钮和指示器
  - GSAP入场动画
- **关于我们**：
  - 公司简介
  - 核心优势
  - 数据统计（项目数量、年营业额等）
- **核心业务**：
  - 玻璃幕墙业务简介与图片
  - 铝合金门窗业务简介与图片
  - 链接到对应专题页
- **精选案例**：
  - 4-6个精选成功案例卡片
  - 轮播展示或网格布局
  - 查看更多按钮链接到案例列表页
- **产品展示**：
  - 主要产品类别展示
  - 图片+简要说明
- **技术优势**：
  - 公司核心技术展示
  - 图标+说明的形式
- **客户评价**：
  - 轮播展示客户评价
- **新闻资讯**：
  - 最新资讯列表（3-5条）
  - 查看更多按钮链接到资讯页
- **联系我们**：
  - 简洁联系表单或联系方式
  - 地图位置展示

#### 5.2.2 玻璃幕墙专题页（glass-curtain-wall.html）
- **页面标题区**：
  - 大标题
  - 简短描述
- **产品介绍**：
  - 玻璃幕墙技术特点
  - 产品优势
  - 应用场景
- **产品系列**：
  - 不同类型玻璃幕墙展示
  - 图片+详细说明
- **技术参数**：
  - 表格形式展示技术参数
- **案例展示**：
  - 玻璃幕墙相关案例
- **常见问题**：
  - 折叠面板形式展示FAQ
- **咨询按钮**：
  - 固定位置的咨询或联系按钮

#### 5.2.3 铝合金门窗专题页（aluminum-doors-windows.html）
- 结构与玻璃幕墙专题页类似，内容针对铝合金门窗产品

#### 5.2.4 成功案例列表（cases.html）
- **筛选区**：
  - 按业务类型筛选（玻璃幕墙/铝合金门窗）
  - 按项目规模筛选
  - 按地区筛选
- **案例网格**：
  - 卡片形式展示案例
  - 每张卡片包含：项目图片、名称、地点、类型、完成时间
  - 分页或无限滚动加载
- **排序功能**：
  - 按时间排序
  - 按热门程度排序

#### 5.2.5 案例详情页（case-detail.html）
- **项目头部**：
  - 项目名称
  - 大型主图
  - 项目基本信息（地点、时间、客户、面积等）
- **项目介绍**：
  - 详细文字描述
  - 项目挑战与解决方案
- **图片展示**：
  - 多图展示，支持放大查看
  - 可能包含施工过程图与完成图对比
- **技术应用**：
  - 项目中使用的技术和产品
- **相关案例**：
  - 推荐类似案例

#### 5.2.6 视频中心（videos.html）
- **视频分类**：
  - 产品介绍
  - 案例展示
  - 企业宣传
  - 施工过程
- **视频列表**：
  - 视频缩略图
  - 标题、时长、发布时间
  - 播放次数
- **筛选与搜索**：
  - 按分类筛选
  - 关键词搜索

#### 5.2.7 视频详情页（video-detail.html）
- **视频播放器**：
  - 响应式设计
  - 支持全屏、播放速度调整
- **视频信息**：
  - 标题、发布时间、分类
  - 详细描述
- **相关视频推荐**

#### 5.2.8 资讯动态（news.html）
- **资讯分类**：
  - 公司新闻
  - 行业资讯
  - 技术前沿
  - 活动公告
- **资讯列表**：
  - 图文卡片形式
  - 包含标题、摘要、发布时间、阅读量、分类标签
- **分页导航**
- **热门资讯侧边栏**

#### 5.2.9 资讯详情页（news-detail.html）
- **资讯标题区**：
  - 标题
  - 发布时间、作者、分类、阅读量
- **资讯内容**：
  - 富文本排版
  - 图片支持
- **标签**：
  - 相关标签链接
- **分享功能**：
  - 社交媒体分享按钮
- **上一篇/下一篇导航**
- **相关资讯推荐**

#### 5.2.10 标签云页面（tags.html）
- **标签云**：
  - 按使用频率显示不同大小的标签
  - 点击标签跳转到对应结果页
- **标签分类**：
  - 按业务类型分组
  - 按资讯类别分组

#### 5.2.11 标签结果页（tag-results.html）
- **当前标签信息**：
  - 标签名称
  - 相关内容数量
- **结果列表**：
  - 混合展示相关案例、资讯、产品
  - 分页或无限滚动

#### 5.2.12 联系我们（contact.html）
- **联系信息**：
  - 公司地址
  - 电话
  - 邮箱
  - 工作时间
- **联系表单**：
  - 姓名、电话、邮箱、主题、留言内容
  - 提交按钮与验证
- **地图**：
  - 嵌入地图显示公司位置
- **分支机构**：
  - 如有多个办公地点，列表展示

#### 5.2.13 关于我们（about.html）
- **公司简介**：
  - 公司历史
  - 使命与愿景
  - 核心价值观
- **发展历程**：
  - 时间线形式展示重要节点
- **团队介绍**：
  - 核心管理层
  - 技术团队
- **资质认证**：
  - 展示公司获得的证书
- **生产基地**：
  - 图片与介绍
- **企业荣誉**：
  - 获得的奖项展示

#### 5.2.14 搜索页面（search.html）
- **搜索框**：
  - 大尺寸搜索输入框
  - 搜索按钮
- **热门搜索**：
  - 热门关键词推荐
- **搜索帮助**：
  - 搜索使用提示

#### 5.2.15 搜索结果页（search-results.html）
- **搜索条件**：
  - 显示当前搜索关键词
  - 提供再次搜索框
- **筛选选项**：
  - 按内容类型筛选（案例、资讯、产品等）
  - 按时间筛选
- **结果列表**：
  - 图文展示搜索结果
  - 高亮显示匹配关键词
- **结果统计**：
  - 显示找到的结果数量
- **分页导航**
- **无结果处理**：
  - 无结果时显示提示信息和推荐内容

#### 5.2.16 404错误页（404.html）
- **错误提示**：
  - 友好的404提示信息
- **搜索框**：
  - 允许用户重新搜索
- **导航链接**：
  - 主要页面快速链接
- **返回首页按钮**

## 6. 交互与动画设计

### 6.1 全局交互
- **平滑滚动**：页面内锚点导航使用平滑滚动
- **导航栏滚动效果**：滚动时导航栏背景变化
- **悬停效果**：按钮、卡片、链接等元素有统一的悬停反馈
- **加载状态**：内容加载时显示加载指示器

### 6.2 GSAP动画应用
- **页面过渡**：页面加载和切换时的过渡动画
- **元素入场**：滚动到视图时的元素入场动画
- **数字计数**：首页数据统计的数字增长动画
- **视差效果**：部分区域使用视差滚动增强深度感
- **交互反馈**：按钮点击、表单提交等交互的微动画

### 6.3 主题切换功能
- **切换按钮**：导航栏中的主题切换图标
- **切换效果**：平滑过渡动画
- **状态记忆**：使用localStorage保存用户主题偏好
- **自动模式**：可选跟随系统主题设置

## 7. 响应式设计

### 7.1 断点设置
采用Bulma的默认断点：
- 移动设备：< 768px
- 平板设备：768px - 1023px
- 桌面设备：1024px - 1215px
- 大型桌面：1216px - 1407px
- 超大型桌面：≥ 1408px

### 7.2 响应式调整策略
- **布局**：从移动端的单列布局逐渐过渡到桌面端的多列布局
- **导航**：移动端使用汉堡菜单，桌面端展示完整导航
- **图片**：使用响应式图片技术，根据设备加载不同尺寸
- **字体**：在极小屏幕上适当减小字号
- **间距**：根据屏幕尺寸调整元素间距，确保在各种设备上都有良好的留白

## 8. 性能优化

### 8.1 图片优化
- 使用现代图片格式（WebP）
- 实现图片懒加载
- 根据设备尺寸提供不同分辨率图片
- 压缩图片文件大小

### 8.2 代码优化
- 压缩CSS和JavaScript文件
- 减少不必要的DOM元素
- 优化关键渲染路径
- 使用异步加载非关键资源

### 8.3 缓存策略
- 设置适当的HTTP缓存头
- 利用localStorage缓存主题设置等小型数据

## 9. 测试计划

### 9.1 功能测试
- 所有页面链接是否正常工作
- 表单提交功能是否正常
- 搜索功能是否准确
- 筛选和排序功能是否正确
- 主题切换是否正常工作

### 9.2 兼容性测试
- 主流浏览器测试（Chrome, Firefox, Safari, Edge）
- 不同设备尺寸测试（手机、平板、桌面）
- 操作系统兼容性（Windows, macOS, iOS, Android）

### 9.3 性能测试
- 页面加载速度测试
- 动画流畅度测试
- 响应时间测试

## 10. 部署说明
- 推荐使用静态网站托管服务（如Netlify, Vercel, GitHub Pages）
- 确保服务器支持适当的缓存策略
- 配置404页面指向自定义404.html
- 考虑使用CDN加速静态资源加载

## 11. 扩展方向
- 多语言支持
- 产品3D展示
- 在线报价系统
- 客户管理系统集成
- 微信小程序关联

---

本开发文档将作为项目开发的指导依据，开发过程中如有调整，应及时更新文档内容。
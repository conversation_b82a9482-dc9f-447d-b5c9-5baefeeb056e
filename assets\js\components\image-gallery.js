// 图片画廊功能

class ImageGallery {
    constructor() {
        this.modal = null;
        this.modalImage = null;
        this.currentImageIndex = 0;
        this.images = [];
        
        this.init();
    }
    
    init() {
        this.setupModal();
        this.bindImageEvents();
        this.bindKeyboardEvents();
        
        console.log('图片画廊功能已初始化');
    }
    
    setupModal() {
        this.modal = document.getElementById('image-modal');
        this.modalImage = document.getElementById('modal-image');
        
        if (!this.modal || !this.modalImage) {
            console.warn('图片模态框元素未找到');
            return;
        }
        
        // 绑定关闭事件
        const closeBtn = this.modal.querySelector('.modal-close');
        const background = this.modal.querySelector('.modal-background');
        
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.closeModal());
        }
        
        if (background) {
            background.addEventListener('click', () => this.closeModal());
        }
    }
    
    bindImageEvents() {
        // 获取所有可点击的图片
        const clickableImages = document.querySelectorAll('img[data-modal-target]');
        
        this.images = Array.from(clickableImages).map((img, index) => {
            return {
                src: img.src,
                alt: img.alt,
                element: img,
                index: index
            };
        });
        
        // 为每个图片绑定点击事件
        clickableImages.forEach((img, index) => {
            img.style.cursor = 'pointer';
            img.addEventListener('click', () => {
                this.openModal(index);
            });
            
            // 添加悬停效果
            img.addEventListener('mouseenter', () => {
                img.style.transform = 'scale(1.05)';
                img.style.transition = 'transform 0.3s ease';
            });
            
            img.addEventListener('mouseleave', () => {
                img.style.transform = 'scale(1)';
            });
        });
    }
    
    bindKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            if (!this.modal || !this.modal.classList.contains('is-active')) {
                return;
            }
            
            switch (e.key) {
                case 'Escape':
                    this.closeModal();
                    break;
                case 'ArrowLeft':
                    this.previousImage();
                    break;
                case 'ArrowRight':
                    this.nextImage();
                    break;
            }
        });
    }
    
    openModal(imageIndex) {
        if (!this.modal || !this.modalImage) {
            return;
        }
        
        this.currentImageIndex = imageIndex;
        const image = this.images[imageIndex];
        
        if (!image) {
            return;
        }
        
        // 设置图片
        this.modalImage.src = image.src;
        this.modalImage.alt = image.alt;
        
        // 显示模态框
        this.modal.classList.add('is-active');
        document.body.classList.add('modal-open');
        
        // 添加导航按钮（如果有多张图片）
        if (this.images.length > 1) {
            this.addNavigationButtons();
        }
        
        // 预加载相邻图片
        this.preloadAdjacentImages();
    }
    
    closeModal() {
        if (!this.modal) {
            return;
        }
        
        this.modal.classList.remove('is-active');
        document.body.classList.remove('modal-open');
        
        // 移除导航按钮
        this.removeNavigationButtons();
    }
    
    addNavigationButtons() {
        // 移除现有的导航按钮
        this.removeNavigationButtons();
        
        const modalContent = this.modal.querySelector('.modal-content');
        
        // 创建上一张按钮
        const prevBtn = document.createElement('button');
        prevBtn.className = 'button is-large gallery-nav gallery-prev';
        prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        prevBtn.setAttribute('aria-label', '上一张图片');
        prevBtn.addEventListener('click', () => this.previousImage());
        
        // 创建下一张按钮
        const nextBtn = document.createElement('button');
        nextBtn.className = 'button is-large gallery-nav gallery-next';
        nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        nextBtn.setAttribute('aria-label', '下一张图片');
        nextBtn.addEventListener('click', () => this.nextImage());
        
        // 添加样式
        const navStyle = `
            .gallery-nav {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                background: rgba(0, 0, 0, 0.5);
                color: white;
                border: none;
                z-index: 1000;
                transition: all 0.3s ease;
            }
            
            .gallery-nav:hover {
                background: rgba(0, 0, 0, 0.8);
                color: white;
            }
            
            .gallery-prev {
                left: 20px;
            }
            
            .gallery-next {
                right: 20px;
            }
            
            .gallery-counter {
                position: absolute;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 14px;
            }
        `;
        
        // 添加样式到页面
        if (!document.getElementById('gallery-styles')) {
            const styleElement = document.createElement('style');
            styleElement.id = 'gallery-styles';
            styleElement.textContent = navStyle;
            document.head.appendChild(styleElement);
        }
        
        // 添加按钮到模态框
        modalContent.appendChild(prevBtn);
        modalContent.appendChild(nextBtn);
        
        // 添加计数器
        const counter = document.createElement('div');
        counter.className = 'gallery-counter';
        counter.textContent = `${this.currentImageIndex + 1} / ${this.images.length}`;
        modalContent.appendChild(counter);
        
        // 更新按钮状态
        this.updateNavigationButtons();
    }
    
    removeNavigationButtons() {
        const navButtons = this.modal.querySelectorAll('.gallery-nav, .gallery-counter');
        navButtons.forEach(btn => btn.remove());
    }
    
    updateNavigationButtons() {
        const prevBtn = this.modal.querySelector('.gallery-prev');
        const nextBtn = this.modal.querySelector('.gallery-next');
        const counter = this.modal.querySelector('.gallery-counter');
        
        if (prevBtn) {
            prevBtn.style.display = this.currentImageIndex > 0 ? 'block' : 'none';
        }
        
        if (nextBtn) {
            nextBtn.style.display = this.currentImageIndex < this.images.length - 1 ? 'block' : 'none';
        }
        
        if (counter) {
            counter.textContent = `${this.currentImageIndex + 1} / ${this.images.length}`;
        }
    }
    
    previousImage() {
        if (this.currentImageIndex > 0) {
            this.currentImageIndex--;
            this.updateModalImage();
        }
    }
    
    nextImage() {
        if (this.currentImageIndex < this.images.length - 1) {
            this.currentImageIndex++;
            this.updateModalImage();
        }
    }
    
    updateModalImage() {
        const image = this.images[this.currentImageIndex];
        
        if (!image || !this.modalImage) {
            return;
        }
        
        // 添加淡出效果
        this.modalImage.style.opacity = '0';
        
        setTimeout(() => {
            this.modalImage.src = image.src;
            this.modalImage.alt = image.alt;
            
            // 图片加载完成后淡入
            this.modalImage.onload = () => {
                this.modalImage.style.opacity = '1';
            };
        }, 150);
        
        // 更新导航按钮
        this.updateNavigationButtons();
        
        // 预加载相邻图片
        this.preloadAdjacentImages();
    }
    
    preloadAdjacentImages() {
        // 预加载前一张图片
        if (this.currentImageIndex > 0) {
            const prevImage = new Image();
            prevImage.src = this.images[this.currentImageIndex - 1].src;
        }
        
        // 预加载后一张图片
        if (this.currentImageIndex < this.images.length - 1) {
            const nextImage = new Image();
            nextImage.src = this.images[this.currentImageIndex + 1].src;
        }
    }
    
    // 添加缩放功能
    addZoomFeature() {
        if (!this.modalImage) {
            return;
        }
        
        let scale = 1;
        let isDragging = false;
        let startX, startY, translateX = 0, translateY = 0;
        
        // 鼠标滚轮缩放
        this.modalImage.addEventListener('wheel', (e) => {
            e.preventDefault();
            
            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            scale *= delta;
            scale = Math.max(0.5, Math.min(3, scale)); // 限制缩放范围
            
            this.modalImage.style.transform = `scale(${scale}) translate(${translateX}px, ${translateY}px)`;
        });
        
        // 拖拽功能
        this.modalImage.addEventListener('mousedown', (e) => {
            if (scale > 1) {
                isDragging = true;
                startX = e.clientX - translateX;
                startY = e.clientY - translateY;
                this.modalImage.style.cursor = 'grabbing';
            }
        });
        
        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                translateX = e.clientX - startX;
                translateY = e.clientY - startY;
                this.modalImage.style.transform = `scale(${scale}) translate(${translateX}px, ${translateY}px)`;
            }
        });
        
        document.addEventListener('mouseup', () => {
            isDragging = false;
            this.modalImage.style.cursor = scale > 1 ? 'grab' : 'default';
        });
        
        // 双击重置
        this.modalImage.addEventListener('dblclick', () => {
            scale = 1;
            translateX = 0;
            translateY = 0;
            this.modalImage.style.transform = 'scale(1) translate(0, 0)';
            this.modalImage.style.cursor = 'default';
        });
    }
}

// 初始化图片画廊
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('image-modal')) {
        window.imageGallery = new ImageGallery();
    }
});

// 暗主题样式定义

[data-theme="dark"] {
  // 基础颜色
  --bg-primary: #{$dark-background};
  --bg-secondary: #{$dark-surface};
  --text-primary: #{$dark-text};
  --text-secondary: #{$dark-text-light};
  --border-color: #{$dark-border};
  
  // 组件颜色
  --navbar-bg: #{rgba($dark-surface, 0.95)};
  --card-bg: #{$dark-surface};
  --footer-bg: #{$black-ter};
  --footer-text: #{$dark-text};
  
  // 阴影 - 暗主题下使用更深的阴影
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.4);
  --shadow-large: 0 16px 32px rgba(0, 0, 0, 0.5);
  
  body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
  }
  
  .navbar {
    background-color: var(--navbar-bg);
    
    .navbar-item {
      color: var(--text-primary);
      
      &:hover {
        color: $primary-light;
        background-color: transparent;
      }
    }
    
    .navbar-burger {
      color: var(--text-primary);
    }
  }
  
  .card {
    background-color: var(--card-bg);
    color: var(--text-primary);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    
    &:hover {
      box-shadow: var(--shadow-medium);
      border-color: rgba($primary, 0.3);
    }
  }
  
  .section {
    background-color: var(--bg-primary);
    
    &.is-alternate {
      background-color: var(--bg-secondary);
    }
  }
  
  .footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
  }
  
  .filter-bar {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    
    .filter-option {
      background-color: var(--card-bg);
      border-color: var(--border-color);
      color: var(--text-primary);
      
      &:hover {
        border-color: $primary-light;
        color: $primary-light;
      }
      
      &.is-active {
        background-color: $primary;
        border-color: $primary;
        color: $white;
      }
    }
  }
  
  .timeline {
    &::before {
      background-color: var(--border-color);
    }
    
    .timeline-item {
      .timeline-content {
        background-color: var(--card-bg);
        color: var(--text-primary);
        box-shadow: var(--shadow-light);
        border: 1px solid var(--border-color);
      }
    }
  }
  
  .breadcrumb {
    a {
      color: var(--text-secondary);
      
      &:hover {
        color: $primary-light;
      }
    }
    
    .is-active a {
      color: var(--text-primary);
    }
  }
  
  .input,
  .textarea,
  .select select {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-primary);
    
    &:focus {
      border-color: $primary-light;
      box-shadow: 0 0 0 0.125em rgba($primary-light, 0.25);
    }
    
    &::placeholder {
      color: var(--text-secondary);
    }
  }
  
  .table {
    background-color: var(--card-bg);
    color: var(--text-primary);
    
    th {
      background-color: var(--bg-secondary);
      color: var(--text-primary);
      border-bottom-color: var(--border-color);
    }
    
    td {
      border-bottom-color: var(--border-color);
    }
    
    tbody tr:hover {
      background-color: var(--bg-secondary);
    }
  }
  
  .modal-card {
    background-color: var(--card-bg);
    
    .modal-card-head,
    .modal-card-foot {
      background-color: var(--bg-secondary);
      border-color: var(--border-color);
    }
    
    .modal-card-title {
      color: var(--text-primary);
    }
    
    .modal-card-body {
      background-color: var(--card-bg);
      color: var(--text-primary);
    }
  }
  
  .dropdown-content {
    background-color: var(--card-bg);
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
    
    .dropdown-item {
      color: var(--text-primary);
      
      &:hover {
        background-color: var(--bg-secondary);
      }
    }
  }
  
  .notification {
    &.is-light {
      background-color: var(--bg-secondary);
      color: var(--text-primary);
    }
  }
  
  .message {
    background-color: var(--bg-secondary);
    
    .message-header {
      background-color: $primary;
      color: $white;
    }
    
    .message-body {
      background-color: var(--card-bg);
      color: var(--text-primary);
      border-color: var(--border-color);
    }
  }
  
  .tabs {
    a {
      color: var(--text-secondary);
      border-bottom-color: var(--border-color);
      
      &:hover {
        color: var(--text-primary);
        border-bottom-color: var(--text-secondary);
      }
    }
    
    .is-active a {
      color: $primary-light;
      border-bottom-color: $primary-light;
    }
  }
  
  .progress {
    background-color: var(--border-color);
    
    &::-webkit-progress-value {
      background-color: $primary-light;
    }
    
    &::-moz-progress-bar {
      background-color: $primary-light;
    }
  }
  
  // 代码块样式
  pre {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
  }
  
  code {
    background-color: var(--bg-secondary);
    color: $primary-light;
  }
  
  // 引用样式
  blockquote {
    background-color: var(--bg-secondary);
    border-left: 4px solid $primary-light;
    color: var(--text-primary);
  }
  
  // 分割线
  hr {
    background-color: var(--border-color);
  }
  
  // 滚动条样式
  ::-webkit-scrollbar-track {
    background-color: var(--bg-secondary);
  }
  
  ::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    
    &:hover {
      background-color: var(--text-secondary);
    }
  }
  
  // 特殊效果增强
  .hero {
    .hero-body {
      .title,
      .subtitle {
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
      }
    }
  }
  
  // 按钮在暗主题下的特殊样式
  .button {
    &.is-light {
      background-color: var(--bg-secondary);
      color: var(--text-primary);
      border-color: var(--border-color);
      
      &:hover {
        background-color: var(--border-color);
      }
    }
  }
}

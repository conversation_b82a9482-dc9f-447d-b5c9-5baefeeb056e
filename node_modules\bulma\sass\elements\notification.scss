@use "../utilities/css-variables" as cv;
@use "../utilities/derived-variables" as dv;
@use "../utilities/initial-variables" as iv;
@use "../utilities/extends";
@use "../utilities/mixins" as mx;

$notification-code-background-color: cv.getVar("scheme-main") !default;
$notification-radius: cv.getVar("radius") !default;
$notification-padding: 1.375em 1.5em !default;

$notification-colors: dv.$colors !default;

.#{iv.$class-prefix}notification {
  @include cv.register-vars(
    (
      "notification-h": #{cv.getVar("scheme-h")},
      "notification-s": #{cv.getVar("scheme-s")},
      "notification-background-l": #{cv.getVar("background-l")},
      "notification-color-l": #{cv.getVar("text-strong-l")},
      "notification-code-background-color": #{$notification-code-background-color},
      "notification-radius": #{$notification-radius},
      "notification-padding": #{$notification-padding},
    )
  );
}

.#{iv.$class-prefix}notification {
  @extend %block;

  background-color: hsl(
    cv.getVar("notification-h"),
    cv.getVar("notification-s"),
    cv.getVar("notification-background-l")
  );
  border-radius: cv.getVar("notification-radius");
  color: hsl(
    cv.getVar("notification-h"),
    cv.getVar("notification-s"),
    cv.getVar("notification-color-l")
  );
  padding: cv.getVar("notification-padding");
  position: relative;

  a:not(.#{iv.$class-prefix}button):not(.#{iv.$class-prefix}dropdown-item) {
    color: currentColor;
    text-decoration: underline;
  }

  strong {
    color: currentColor;
  }

  code,
  pre {
    background: cv.getVar("notification-code-background-color");
  }

  pre code {
    background: transparent;
  }

  & > .#{iv.$class-prefix}delete {
    position: absolute;
    inset-inline-end: 1rem;
    top: 1rem;
  }

  .#{iv.$class-prefix}title,
  .#{iv.$class-prefix}subtitle,
  .#{iv.$class-prefix}content {
    color: currentColor;
  }

  // Colors
  @each $name, $pair in $notification-colors {
    &.#{iv.$class-prefix}is-#{$name} {
      @include cv.register-vars(
        (
          "notification-h": #{cv.getVar($name, "", "-h")},
          "notification-s": #{cv.getVar($name, "", "-s")},
          "notification-background-l": #{cv.getVar($name, "", "-l")},
          "notification-color-l": #{cv.getVar($name, "", "-invert-l")},
        )
      );

      &.#{iv.$class-prefix}is-light {
        @include cv.register-vars(
          (
            "notification-background-l": 90%,
            "notification-color-l": #{cv.getVar($name, "", "-light-invert-l")},
          )
        );
      }

      &.#{iv.$class-prefix}is-dark {
        @include cv.register-vars(
          (
            "notification-background-l": 20%,
            "notification-color-l": #{cv.getVar($name, "", "-dark-invert-l")},
          )
        );
      }
    }
  }
}
